<?php
/**
 * User Model
 * 
 * This class handles user-related database operations.
 */
class User {
    private $pdo;
    
    /**
     * Constructor
     * 
     * @param PDO $pdo The PDO instance
     */
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }
    
    /**
     * Get all users
     * 
     * @param int $hospitalId Optional hospital ID to filter users
     * @return array The users
     */
    public function getAll($hospitalId = null) {
        try {
            $sql = "
                SELECT u.*, h.name AS hospital_name
                FROM users u
                LEFT JOIN hospitals h ON u.hospital_id = h.id
            ";
            $params = [];

            if ($hospitalId) {
                $sql .= " WHERE u.hospital_id = ?";
                $params[] = $hospitalId;
            }

            $sql .= " ORDER BY u.full_name";

            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);

            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Get All Users Error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get filtered users
     *
     * @param int $hospitalId Optional hospital ID to filter users
     * @param string $role Optional role to filter users
     * @param string $status Optional status to filter users
     * @param string $search Optional search term
     * @return array The users
     */
    public function getFiltered($hospitalId = null, $role = null, $status = null, $search = null) {
        try {
            $sql = "
                SELECT u.*, h.name AS hospital_name
                FROM users u
                LEFT JOIN hospitals h ON u.hospital_id = h.id
                WHERE 1=1
            ";
            $params = [];

            if ($hospitalId) {
                $sql .= " AND u.hospital_id = ?";
                $params[] = $hospitalId;
            }

            if ($role) {
                $sql .= " AND u.role = ?";
                $params[] = $role;
            }

            if ($status) {
                $sql .= " AND u.status = ?";
                $params[] = $status;
            }

            if ($search) {
                $sql .= " AND (u.full_name LIKE ? OR u.username LIKE ? OR u.email LIKE ?)";
                $searchTerm = '%' . $search . '%';
                $params[] = $searchTerm;
                $params[] = $searchTerm;
                $params[] = $searchTerm;
            }

            $sql .= " ORDER BY u.full_name";

            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);

            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Get Filtered Users Error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get a user by ID
     * 
     * @param int $id The user ID
     * @return array|bool The user or false if not found
     */
    public function getById($id) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT u.*, h.name AS hospital_name
                FROM users u
                LEFT JOIN hospitals h ON u.hospital_id = h.id
                WHERE u.id = ?
            ");
            
            $stmt->execute([$id]);
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("Get User By ID Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get a user by username
     * 
     * @param string $username The username
     * @return array|bool The user or false if not found
     */
    public function getByUsername($username) {
        try {
            $stmt = $this->pdo->prepare("SELECT * FROM users WHERE username = ?");
            $stmt->execute([$username]);
            
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("Get User By Username Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get a user by email
     * 
     * @param string $email The email
     * @return array|bool The user or false if not found
     */
    public function getByEmail($email) {
        try {
            $stmt = $this->pdo->prepare("SELECT * FROM users WHERE email = ?");
            $stmt->execute([$email]);
            
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("Get User By Email Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Create a new user
     * 
     * @param array $data The user data
     * @return int|bool The user ID if successful, false otherwise
     */
    public function create($data) {
        try {
            // Hash the password
            $hashedPassword = password_hash($data['password'], PASSWORD_DEFAULT);
            
            // Set default permissions based on role
            $permissions = function_exists('getDefaultPermissions')
                ? getDefaultPermissions($data['role'])
                : $this->getDefaultPermissionsForRole($data['role']);
            
            $stmt = $this->pdo->prepare("
                INSERT INTO users (
                    username, password, email, full_name, role, permissions, hospital_id, language, status
                ) VALUES (
                    ?, ?, ?, ?, ?, ?, ?, ?, ?
                )
            ");

            $stmt->execute([
                $data['username'],
                $hashedPassword,
                $data['email'],
                $data['full_name'],
                $data['role'],
                json_encode($permissions),
                $data['hospital_id'],
                $data['language'] ?? 'en',
                $data['status'] ?? 'active'
            ]);
            
            return $this->pdo->lastInsertId();
        } catch (PDOException $e) {
            error_log("Create User Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update a user
     * 
     * @param int $id The user ID
     * @param array $data The user data
     * @return bool True if successful, false otherwise
     */
    public function update($id, $data) {
        try {
            $sql = "
                UPDATE users SET
                    email = ?,
                    full_name = ?,
                    role = ?,
                    permissions = ?,
                    hospital_id = ?,
                    language = ?,
                    status = ?,
                    updated_at = NOW()
            ";

            $params = [
                $data['email'],
                $data['full_name'],
                $data['role'],
                json_encode($data['permissions'] ?? (function_exists('getDefaultPermissions')
                    ? getDefaultPermissions($data['role'])
                    : $this->getDefaultPermissionsForRole($data['role']))),
                $data['hospital_id'],
                $data['language'] ?? 'en',
                $data['status'] ?? 'active'
            ];
            
            // If password is being updated
            if (!empty($data['password'])) {
                $hashedPassword = password_hash($data['password'], PASSWORD_DEFAULT);
                $sql .= ", password = ?";
                $params[] = $hashedPassword;
            }
            
            $sql .= " WHERE id = ?";
            $params[] = $id;
            
            $stmt = $this->pdo->prepare($sql);
            return $stmt->execute($params);
        } catch (PDOException $e) {
            error_log("Update User Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Update user status
     *
     * @param int $id The user ID
     * @param string $status The new status
     * @return bool True if successful, false otherwise
     */
    public function updateStatus($id, $status) {
        try {
            $stmt = $this->pdo->prepare("
                UPDATE users SET
                    status = ?,
                    updated_at = NOW()
                WHERE id = ?
            ");

            return $stmt->execute([$status, $id]);
        } catch (PDOException $e) {
            error_log("Update User Status Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Delete a user
     * 
     * @param int $id The user ID
     * @return bool True if successful, false otherwise
     */
    public function delete($id) {
        try {
            // Check if the user is the last admin
            $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM users WHERE role = 'admin'");
            $stmt->execute();
            $adminCount = $stmt->fetchColumn();
            
            $stmt = $this->pdo->prepare("SELECT role FROM users WHERE id = ?");
            $stmt->execute([$id]);
            $user = $stmt->fetch();
            
            if ($adminCount <= 1 && $user['role'] === 'admin') {
                // Cannot delete the last admin
                return false;
            }
            
            $stmt = $this->pdo->prepare("DELETE FROM users WHERE id = ?");
            return $stmt->execute([$id]);
        } catch (PDOException $e) {
            error_log("Delete User Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get users by role
     * 
     * @param string $role The role
     * @param int $hospitalId Optional hospital ID to filter users
     * @return array The users
     */
    public function getByRole($role, $hospitalId = null) {
        try {
            $sql = "
                SELECT u.*, h.name AS hospital_name
                FROM users u
                LEFT JOIN hospitals h ON u.hospital_id = h.id
                WHERE u.role = ?
            ";
            $params = [$role];

            if ($hospitalId) {
                $sql .= " AND u.hospital_id = ?";
                $params[] = $hospitalId;
            }

            $sql .= " ORDER BY u.full_name";

            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);

            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Get Users By Role Error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get users by permission
     * 
     * @param string $permission The permission
     * @param int $hospitalId Optional hospital ID to filter users
     * @return array The users
     */
    public function getByPermission($permission, $hospitalId = null) {
        try {
            $sql = "
                SELECT u.*, h.name AS hospital_name
                FROM users u
                LEFT JOIN hospitals h ON u.hospital_id = h.id
                WHERE (u.role = 'admin' OR JSON_CONTAINS(u.permissions, ?, '$'))
            ";
            $params = ['"' . $permission . '"'];

            if ($hospitalId) {
                $sql .= " AND u.hospital_id = ?";
                $params[] = $hospitalId;
            }

            $sql .= " ORDER BY u.full_name";

            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);

            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Get Users By Permission Error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Count users
     * 
     * @param int $hospitalId Optional hospital ID to filter users
     * @return int The number of users
     */
    public function count($hospitalId = null) {
        try {
            $sql = "SELECT COUNT(*) FROM users";
            $params = [];
            
            if ($hospitalId) {
                $sql .= " WHERE hospital_id = ?";
                $params[] = $hospitalId;
            }
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            
            return (int) $stmt->fetchColumn();
        } catch (PDOException $e) {
            error_log("Count Users Error: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Count users by role
     *
     * @param string $role The role
     * @param int $hospitalId Optional hospital ID to filter users
     * @return int The number of users
     */
    public function countByRole($role, $hospitalId = null) {
        try {
            $sql = "SELECT COUNT(*) FROM users WHERE role = ?";
            $params = [$role];

            if ($hospitalId) {
                $sql .= " AND hospital_id = ?";
                $params[] = $hospitalId;
            }

            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);

            return (int) $stmt->fetchColumn();
        } catch (PDOException $e) {
            error_log("Count Users By Role Error: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Get default permissions for a role (fallback method)
     *
     * @param string $role The role
     * @return array The default permissions
     */
    private function getDefaultPermissionsForRole($role) {
        $permissions = [
            'admin' => [
                'view_dashboard', 'manage_users', 'manage_hospitals', 'manage_departments',
                'manage_devices', 'manage_maintenance', 'manage_tickets', 'view_reports',
                'export_data', 'manage_roles', 'view_roles'
            ],
            'engineer' => [
                'view_dashboard', 'view_users', 'view_hospitals', 'manage_departments',
                'manage_devices', 'manage_maintenance', 'manage_tickets', 'view_reports',
                'export_data'
            ],
            'technician' => [
                'view_dashboard', 'view_departments', 'view_devices', 'manage_maintenance',
                'manage_tickets', 'view_reports'
            ],
            'staff' => [
                'view_dashboard', 'view_departments', 'view_devices', 'view_maintenance',
                'view_tickets'
            ]
        ];

        return $permissions[$role] ?? $permissions['staff'];
    }
}
