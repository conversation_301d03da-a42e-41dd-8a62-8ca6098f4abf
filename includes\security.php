<?php
/**
 * Security Functions
 * 
 * This file contains functions for security-related operations.
 */

/**
 * Generate a CSRF token
 * 
 * @return string The CSRF token
 */
function generateCSRFToken() {
    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    // Generate a new token if one doesn't exist
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    
    return $_SESSION['csrf_token'];
}

/**
 * Verify CSRF token
 *
 * @param string $token The token to verify
 * @return bool True if token is valid, false otherwise
 */
function verifyCSRFToken($token) {
    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // Check if token exists and matches
    if (isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token)) {
        return true;
    }

    return false;
}

/**
 * Validate CSRF token (alias for verifyCSRFToken)
 *
 * @param string $token The token to validate
 * @return bool True if token is valid, false otherwise
 */
function validateCSRFToken($token) {
    return verifyCSRFToken($token);
}

/**
 * Check for CSRF token in POST requests
 * 
 * @return void
 */
function checkCSRFToken() {
    // Only check POST requests
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Check if token exists and is valid
        if (!isset($_POST['csrf_token']) || !verifyCSRFToken($_POST['csrf_token'])) {
            // Log the attempt
            error_log('CSRF token validation failed: ' . $_SERVER['REQUEST_URI']);
            
            // Redirect to error page
            header('Location: ' . getBaseUrl() . '/error?code=403&message=' . urlencode(__('csrf_error')));
            exit;
        }
    }
}

/**
 * Rate limiting for login attempts
 * 
 * @param string $username The username
 * @param bool $success Whether the login was successful
 * @return bool|array True if allowed, array with wait time if blocked
 */
function checkLoginRateLimit($username, $success = false) {
    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    // Initialize login attempts if not set
    if (!isset($_SESSION['login_attempts'])) {
        $_SESSION['login_attempts'] = [];
    }
    
    // Get current time
    $now = time();
    
    // Clean up old attempts (older than 1 hour)
    foreach ($_SESSION['login_attempts'] as $user => $attempts) {
        foreach ($attempts as $index => $attempt) {
            if ($attempt['time'] < ($now - 3600)) {
                unset($_SESSION['login_attempts'][$user][$index]);
            }
        }
        
        // Remove user if no attempts left
        if (empty($_SESSION['login_attempts'][$user])) {
            unset($_SESSION['login_attempts'][$user]);
        }
    }
    
    // If login was successful, reset attempts for this user
    if ($success) {
        unset($_SESSION['login_attempts'][$username]);
        return true;
    }
    
    // Initialize user attempts if not set
    if (!isset($_SESSION['login_attempts'][$username])) {
        $_SESSION['login_attempts'][$username] = [];
    }
    
    // Count recent attempts (last 15 minutes)
    $recentAttempts = 0;
    foreach ($_SESSION['login_attempts'][$username] as $attempt) {
        if ($attempt['time'] > ($now - 900)) {
            $recentAttempts++;
        }
    }
    
    // Check if user is blocked
    if ($recentAttempts >= 5) {
        // Find the oldest recent attempt
        $oldestTime = $now;
        foreach ($_SESSION['login_attempts'][$username] as $attempt) {
            if ($attempt['time'] > ($now - 900) && $attempt['time'] < $oldestTime) {
                $oldestTime = $attempt['time'];
            }
        }
        
        // Calculate wait time (15 minutes from oldest recent attempt)
        $waitTime = 900 - ($now - $oldestTime);
        
        return [
            'blocked' => true,
            'wait_time' => $waitTime,
            'wait_minutes' => ceil($waitTime / 60)
        ];
    }
    
    // Add this attempt
    $_SESSION['login_attempts'][$username][] = [
        'time' => $now,
        'ip' => $_SERVER['REMOTE_ADDR']
    ];
    
    return true;
}

/**
 * Sanitize input
 * 
 * @param string $input The input to sanitize
 * @return string The sanitized input
 */
function sanitizeInput($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

/**
 * Validate email
 * 
 * @param string $email The email to validate
 * @return bool True if valid, false otherwise
 */
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * Validate URL
 * 
 * @param string $url The URL to validate
 * @return bool True if valid, false otherwise
 */
function validateURL($url) {
    return filter_var($url, FILTER_VALIDATE_URL) !== false;
}

/**
 * Validate date
 * 
 * @param string $date The date to validate (YYYY-MM-DD)
 * @return bool True if valid, false otherwise
 */
function validateDate($date) {
    $d = DateTime::createFromFormat('Y-m-d', $date);
    return $d && $d->format('Y-m-d') === $date;
}

/**
 * Validate password strength
 *
 * @param string $password The password to validate
 * @return array Result with status and message
 */
function validatePasswordStrength($password) {
    $result = [
        'valid' => true,
        'message' => ''
    ];

    // Check length (standardized to 8 characters minimum)
    if (strlen($password) < 8) {
        $result['valid'] = false;
        $result['message'] = __('password_too_short');
        return $result;
    }
    
    // Check for uppercase
    if (!preg_match('/[A-Z]/', $password)) {
        $result['valid'] = false;
        $result['message'] = __('password_no_uppercase');
        return $result;
    }
    
    // Check for lowercase
    if (!preg_match('/[a-z]/', $password)) {
        $result['valid'] = false;
        $result['message'] = __('password_no_lowercase');
        return $result;
    }
    
    // Check for numbers
    if (!preg_match('/[0-9]/', $password)) {
        $result['valid'] = false;
        $result['message'] = __('password_no_number');
        return $result;
    }
    
    // Check for special characters
    if (!preg_match('/[^A-Za-z0-9]/', $password)) {
        $result['valid'] = false;
        $result['message'] = __('password_no_special');
        return $result;
    }
    
    return $result;
}

/**
 * Generate a secure random password
 * 
 * @param int $length The password length
 * @return string The generated password
 */
function generateSecurePassword($length = 12) {
    $chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()-_=+';
    $password = '';
    
    // Ensure at least one of each character type
    $password .= $chars[random_int(0, 25)]; // Uppercase
    $password .= $chars[random_int(26, 51)]; // Lowercase
    $password .= $chars[random_int(52, 61)]; // Number
    $password .= $chars[random_int(62, strlen($chars) - 1)]; // Special
    
    // Fill the rest randomly
    for ($i = 4; $i < $length; $i++) {
        $password .= $chars[random_int(0, strlen($chars) - 1)];
    }
    
    // Shuffle the password
    return str_shuffle($password);
}

/**
 * Log security event
 * 
 * @param string $event The event to log
 * @param string $username The username
 * @param string $ip The IP address
 * @return void
 */
function logSecurityEvent($event, $username = '', $ip = '') {
    global $pdo;
    
    // Get IP if not provided
    if (empty($ip)) {
        $ip = $_SERVER['REMOTE_ADDR'];
    }
    
    // Get username if not provided
    if (empty($username) && isset($_SESSION['user'])) {
        $username = $_SESSION['user']['username'];
    }
    
    // Insert log entry
    try {
        $stmt = $pdo->prepare("
            INSERT INTO security_logs (
                event, username, ip_address, created_at
            ) VALUES (
                ?, ?, ?, NOW()
            )
        ");
        
        $stmt->execute([$event, $username, $ip]);
    } catch (PDOException $e) {
        error_log("Security Log Error: " . $e->getMessage());
    }
}





/**
 * Check if user has permission
 *
 * @param string $permission The permission to check
 * @param bool $allowAdminBypass Whether to allow admin to bypass permission check (default: true)
 * @return bool True if user has permission, false otherwise
 */
function hasPermission($permission, $allowAdminBypass = true) {
    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // Check if user is logged in
    if (!isset($_SESSION['user'])) {
        return false;
    }

    // Admin has all permissions (unless explicitly disabled)
    if ($allowAdminBypass && $_SESSION['user']['role'] === 'admin') {
        return true;
    }

    // Check user permissions
    if (isset($_SESSION['user']['permissions']) && is_array($_SESSION['user']['permissions'])) {
        return in_array($permission, $_SESSION['user']['permissions']);
    }

    return false;
}

/**
 * Require login
 * 
 * @return void
 */
function requireLogin() {
    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    // Check if user is logged in
    if (!isset($_SESSION['user'])) {
        // Store current URL for redirect after login
        $_SESSION['redirect_after_login'] = $_SERVER['REQUEST_URI'];
        
        // Redirect to login page
        header('Location: ' . getBaseUrl() . '/login');
        exit;
    }
}

/**
 * Require permission
 * 
 * @param string $permission The required permission
 * @return void
 */
function requirePermission($permission) {
    // Require login first
    requireLogin();
    
    // Check permission
    if (!hasPermission($permission)) {
        // Log the attempt
        logSecurityEvent('permission_denied', '', '');
        
        // Redirect to error page
        header('Location: ' . getBaseUrl() . '/error?code=403&message=' . urlencode(__('permission_denied')));
        exit;
    }
}
