<?php
/**
 * Login Page
 * 
 * This file handles user authentication.
 */

// Start session
session_start();

// Set default timezone
date_default_timezone_set('UTC');

// Load configuration
require_once 'config/database.php';

// Load includes
require_once 'includes/functions.php';
require_once 'includes/auth.php';
require_once 'includes/security.php';

// Check if user is already logged in
if (isLoggedIn()) {
    redirect('index.php');
}

// Handle language switching
if (isset($_GET['lang']) && in_array($_GET['lang'], ['en', 'ar'])) {
    setLanguage($_GET['lang']);
    redirect('login.php');
}

// Handle login form submission
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    $remember = isset($_POST['remember']);
    
    if (empty($username) || empty($password)) {
        $error = __('required_field');
    } else {
        if (authenticate($username, $password)) {
            // Set remember me cookie if requested
            if ($remember) {
                $token = bin2hex(random_bytes(32));
                $expires = time() + (86400 * 30); // 30 days
                
                setcookie('remember_token', $token, $expires, '/');
                
                // Store the token in the database
                $stmt = $pdo->prepare("
                    INSERT INTO remember_tokens (user_id, token, expires_at)
                    VALUES (?, ?, ?)
                ");
                
                $stmt->execute([
                    $_SESSION['user']['id'],
                    $token,
                    date('Y-m-d H:i:s', $expires)
                ]);
            }
            
            redirect('dashboard');
        } else {
            $error = __('login_failed');
        }
    }
}

// Clean up expired remember tokens periodically
cleanupExpiredRememberTokens();

// Check for remember me cookie
if (!isLoggedIn() && isset($_COOKIE['remember_token'])) {
    $token = $_COOKIE['remember_token'];
    
    $stmt = $pdo->prepare("
        SELECT user_id FROM remember_tokens
        WHERE token = ? AND expires_at > NOW()
    ");
    
    $stmt->execute([$token]);
    $result = $stmt->fetch();
    
    if ($result) {
        $userId = $result['user_id'];
        
        $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$userId]);
        $user = $stmt->fetch();
        
        if ($user) {
            // Regenerate session ID to prevent session fixation
            session_regenerate_id(true);

            // Decode permissions from JSON
            $permissions = json_decode($user['permissions'], true);
            if (!is_array($permissions)) {
                $permissions = getDefaultPermissions($user['role']);
            }

            // Set session variables
            $_SESSION['user'] = [
                'id' => $user['id'],
                'username' => $user['username'],
                'full_name' => $user['full_name'],
                'email' => $user['email'],
                'role' => $user['role'],
                'permissions' => $permissions,
                'hospital_id' => $user['hospital_id'],
                'language' => $user['language']
            ];

            // Set the user's language preference
            setLanguage($user['language']);

            // Update last login time
            $stmt = $pdo->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
            $stmt->execute([$user['id']]);

            // Log the login action
            logAction('login', 'User logged in via remember me cookie');

            redirect('dashboard');
        }
    }
    
    // Invalid or expired token, clear the cookie
    setcookie('remember_token', '', time() - 3600, '/');
}

// Get the current language
$currentLanguage = getCurrentLanguage();

// Check if the database needs to be initialized
$stmt = $pdo->query("SHOW TABLES");
$tables = $stmt->fetchAll(PDO::FETCH_COLUMN);

if (empty($tables)) {
    // Database is empty, redirect to installation
    redirect('database/install.php');
}
?>
<!DOCTYPE html>
<html lang="<?php echo $currentLanguage; ?>" dir="<?php echo $currentLanguage === 'ar' ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo __('login'); ?> - <?php echo __('app_name'); ?></title>
    
    <!-- Bootstrap CSS -->
    <?php if ($currentLanguage === 'ar'): ?>
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <?php else: ?>
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <?php endif; ?>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body class="bg-light">
    <!-- Background Animation -->
    <div class="login-bg">
        <div class="login-bg-shapes">
            <div class="shape shape-1"></div>
            <div class="shape shape-2"></div>
            <div class="shape shape-3"></div>
        </div>
    </div>

    <div class="container">
        <div class="row justify-content-center align-items-center min-vh-100">
            <div class="col-lg-5 col-md-7">
                <div class="card login-card shadow-lg border-0 fade-in-up">
                    <div class="card-body p-5">
                        <div class="text-center mb-5">
                            <div class="login-logo mb-4">
                                <div class="logo-icon">
                                    <i class="fas fa-heartbeat text-primary"></i>
                                </div>
                            </div>
                            <h1 class="h3 text-gradient mb-2"><?php echo __('app_name'); ?></h1>
                            <p class="text-muted"><?php echo __('secure_login_portal'); ?></p>
                        </div>
                        
                        <?php if ($error): ?>
                            <div class="alert alert-danger border-0 shadow-sm slide-in-right">
                                <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
                            </div>
                        <?php endif; ?>

                        <form method="post" action="login.php" id="loginForm">
                            <div class="mb-4">
                                <label for="username" class="form-label"><?php echo __('username'); ?></label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-user"></i>
                                    </span>
                                    <input type="text" class="form-control" id="username" name="username" required
                                           placeholder="<?php echo __('enter_username'); ?>">
                                </div>
                            </div>

                            <div class="mb-4">
                                <label for="password" class="form-label"><?php echo __('password'); ?></label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-lock"></i>
                                    </span>
                                    <input type="password" class="form-control" id="password" name="password" required
                                           placeholder="<?php echo __('enter_password'); ?>">
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword()">
                                        <i class="fas fa-eye" id="passwordToggle"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="mb-4">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" id="remember" name="remember">
                                    <label class="form-check-label" for="remember"><?php echo __('remember_me'); ?></label>
                                </div>
                            </div>

                            <div class="d-grid gap-2 mb-4">
                                <button type="submit" class="btn btn-primary btn-lg" id="loginBtn">
                                    <span class="btn-text"><?php echo __('login'); ?></span>
                                    <span class="loading-spinner d-none"></span>
                                </button>
                            </div>
                        </form>
                        
                        <div class="mt-3 text-center">
                            <a href="reset_password.php"><?php echo __('forgot_password'); ?></a>
                        </div>
                        
                        <div class="mt-4 text-center">
                            <div class="language-toggle">
                                <div class="btn-group" role="group">
                                    <a href="?lang=en" class="btn btn-sm <?php echo $currentLanguage === 'en' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                                        <i class="fas fa-flag-usa me-1"></i>English
                                    </a>
                                    <a href="?lang=ar" class="btn btn-sm <?php echo $currentLanguage === 'ar' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                                        <i class="fas fa-flag me-1"></i>العربية
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Login Page Scripts -->
    <script>
        // Toggle password visibility
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const passwordToggle = document.getElementById('passwordToggle');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                passwordToggle.classList.remove('fa-eye');
                passwordToggle.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                passwordToggle.classList.remove('fa-eye-slash');
                passwordToggle.classList.add('fa-eye');
            }
        }

        // Form submission with loading state
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            const loginBtn = document.getElementById('loginBtn');
            const btnText = loginBtn.querySelector('.btn-text');
            const spinner = loginBtn.querySelector('.loading-spinner');

            // Show loading state
            btnText.classList.add('d-none');
            spinner.classList.remove('d-none');
            loginBtn.disabled = true;

            // Re-enable after 3 seconds if form doesn't submit
            setTimeout(() => {
                btnText.classList.remove('d-none');
                spinner.classList.add('d-none');
                loginBtn.disabled = false;
            }, 3000);
        });

        // Add focus effects
        document.querySelectorAll('.form-control').forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.classList.add('focused');
            });

            input.addEventListener('blur', function() {
                this.parentElement.classList.remove('focused');
            });
        });

        // Animate elements on page load
        document.addEventListener('DOMContentLoaded', function() {
            const card = document.querySelector('.login-card');
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';

            setTimeout(() => {
                card.style.transition = 'all 0.6s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 100);
        });
    </script>
</body>
</html>
