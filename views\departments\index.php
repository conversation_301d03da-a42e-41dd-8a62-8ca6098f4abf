<?php
/**
 * Departments List View
 * 
 * This file displays the list of departments.
 */

// Set page title
$pageTitle = __('departments');
$pageSubtitle = __('manage_hospital_departments');

// Start output buffering
ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 text-gradient mb-1"><?php echo __('departments_management'); ?></h1>
        <p class="text-muted mb-0"><?php echo __('manage_hospital_departments'); ?></p>
    </div>

    <div class="d-flex gap-2">
        <?php if (hasPermission('manage_departments')): ?>
        <a href="<?php echo getBaseUrl(); ?>/departments/create" class="btn btn-primary">
            <?php echo __('add_department'); ?>
        </a>
        <?php endif; ?>

        <div class="dropdown">
            <button class="btn btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                <?php echo __('export'); ?>
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="<?php echo getBaseUrl(); ?>/departments/export?format=pdf">
                    PDF
                </a></li>
                <li><a class="dropdown-item" href="<?php echo getBaseUrl(); ?>/departments/export?format=excel">
                    Excel
                </a></li>
            </ul>
        </div>
    </div>
</div>

<!-- Enhanced Filters -->
<?php if (hasPermission('view_hospitals')): ?>
<div class="card card-glass mb-4 fade-in-up">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <?php echo __('filters'); ?>
        </h5>
    </div>
    <div class="card-body">
        <form action="<?php echo getBaseUrl(); ?>/departments" method="get" class="row g-3">
            <div class="col-md-4">
                <label for="hospital_id" class="form-label"><?php echo __('filter_by_hospital'); ?></label>
                <select class="form-select" id="hospital_id" name="hospital_id">
                    <option value=""><?php echo __('all_hospitals'); ?></option>
                    <?php foreach ($hospitals as $hospital): ?>
                        <option value="<?php echo $hospital['id']; ?>" <?php echo (isset($_GET['hospital_id']) && $_GET['hospital_id'] == $hospital['id']) ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($hospital['name']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="submit" class="btn btn-primary">
                    <?php echo __('filter'); ?>
                </button>
            </div>
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearFilters()">
                        <?php echo __('clear_filters'); ?>
                    </button>
                    <small class="text-muted">
                        <?php echo count($departments); ?> <?php echo __('departments_found'); ?>
                    </small>
                </div>
            </div>
        </form>
    </div>
</div>
<?php endif; ?>

<!-- Enhanced Departments Table -->
<div class="card fade-in-up" style="animation-delay: 0.2s;">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <?php echo __('departments_list'); ?>
            </h5>
        </div>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0" id="departments-table">
                <thead>
                    <tr>
                        <th><?php echo __('name'); ?></th>
                        <th><?php echo __('hospital'); ?></th>
                        <th><?php echo __('location'); ?></th>
                        <th><?php echo __('phone'); ?></th>
                        <th><?php echo __('devices'); ?></th>
                        <th><?php echo __('actions'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($departments)): ?>
                        <tr>
                            <td colspan="6" class="text-center"><?php echo __('no_departments'); ?></td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($departments as $department): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($department['name']); ?></td>
                                <td>
                                    <?php if (hasPermission('view_hospitals')): ?>
                                        <a href="<?php echo getBaseUrl(); ?>/hospitals/view/<?php echo $department['hospital_id']; ?>">
                                            <?php echo htmlspecialchars($department['hospital_name']); ?>
                                        </a>
                                    <?php else: ?>
                                        <?php echo htmlspecialchars($department['hospital_name']); ?>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo htmlspecialchars($department['location']); ?></td>
                                <td><?php echo htmlspecialchars($department['phone']); ?></td>
                                <td><?php echo (int)$department['device_count']; ?></td>
                                <td>
                                    <div class="btn-group">
                                        <a href="<?php echo getBaseUrl(); ?>/departments/view/<?php echo $department['id']; ?>" class="btn btn-sm btn-outline-primary">
                                            View
                                        </a>

                                        <?php if (hasPermission('manage_departments')): ?>
                                            <a href="<?php echo getBaseUrl(); ?>/departments/edit/<?php echo $department['id']; ?>" class="btn btn-sm btn-outline-secondary">
                                                Edit
                                            </a>

                                            <button type="button" class="btn btn-sm btn-outline-danger"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#deleteModal"
                                                    data-id="<?php echo $department['id']; ?>"
                                                    data-name="<?php echo htmlspecialchars($department['name']); ?>">
                                                Delete
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Export Options -->
<div class="card mt-4">
    <div class="card-header">
        <h5 class="card-title mb-0"><?php echo __('export_options'); ?></h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <form action="<?php echo getBaseUrl(); ?>/departments/export" method="post">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <?php if (isset($_GET['hospital_id'])): ?>
                        <input type="hidden" name="hospital_id" value="<?php echo $_GET['hospital_id']; ?>">
                    <?php endif; ?>
                    <div class="mb-3">
                        <label for="export_type" class="form-label"><?php echo __('export_format'); ?></label>
                        <select class="form-select" id="export_type" name="export_type">
                            <option value="pdf">PDF</option>
                            <option value="excel">Excel</option>
                            <option value="csv">CSV</option>
                        </select>
                    </div>
                    <button type="submit" class="btn btn-success">
                        <?php echo __('export'); ?>
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel"><?php echo __('confirm_delete'); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p><?php echo __('confirm_delete_department'); ?> <span id="departmentName"></span>?</p>
                <p class="text-danger"><?php echo __('delete_department_warning'); ?></p>
            </div>
            <div class="modal-footer">
                <form action="<?php echo getBaseUrl(); ?>/departments/delete" method="post">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="id" id="deleteId">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo __('cancel'); ?></button>
                    <button type="submit" class="btn btn-danger"><?php echo __('delete'); ?></button>
                </form>
            </div>
        </div>
    </div>
</div>

<?php
// Add scripts
$scripts = '
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
<script>
    $(document).ready(function() {
        // Initialize DataTable
        $("#departments-table").DataTable({
            "language": {
                "url": "' . ($currentLanguage === "ar" ? "//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json" : "//cdn.datatables.net/plug-ins/1.11.5/i18n/en-GB.json") . '"
            },
            "order": [[0, "asc"]]
        });
        
        // Set up delete modal
        $("#deleteModal").on("show.bs.modal", function(event) {
            var button = $(event.relatedTarget);
            var id = button.data("id");
            var name = button.data("name");
            
            $("#deleteId").val(id);
            $("#departmentName").text(name);
        });
        
        // Auto-submit filter form when hospital changes
        $("#hospital_id").change(function() {
            $(this).closest("form").submit();
        });
    });

    function clearFilters() {
        window.location.href = "<?php echo getBaseUrl(); ?>/departments";
    }
</script>
';

// Get the content
$content = ob_get_clean();

// Include the layout
include 'views/layout.php';
?>
