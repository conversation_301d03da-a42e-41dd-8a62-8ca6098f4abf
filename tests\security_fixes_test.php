<?php
/**
 * Security Fixes Test
 *
 * This file tests all the security fixes that were implemented.
 */

// Include only necessary files
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/security.php';

echo "=== Security Fixes Test ===\n\n";

// Test 1: CSRF Token Generation and Validation
echo "1. Testing CSRF Token Functionality...\n";
session_start();

// Generate token
$token1 = generateCSRFToken();
echo "   Generated token: " . substr($token1, 0, 10) . "...\n";

// Verify same token
$isValid1 = verifyCSRFToken($token1);
echo "   Token validation (same token): " . ($isValid1 ? "PASS" : "FAIL") . "\n";

// Verify token again (should still work - no regeneration)
$isValid2 = verifyCSRFToken($token1);
echo "   Token validation (reuse): " . ($isValid2 ? "PASS" : "FAIL") . "\n";

// Test invalid token
$isValid3 = verifyCSRFToken('invalid_token');
echo "   Invalid token validation: " . ($isValid3 ? "FAIL" : "PASS") . "\n";

// Test 2: Input Sanitization
echo "\n2. Testing Input Sanitization...\n";

$testInputs = [
    '<script>alert("xss")</script>' => '&lt;script&gt;alert(&quot;xss&quot;)&lt;/script&gt;',
    'Normal text' => 'Normal text',
    '  whitespace  ' => 'whitespace',
    "null\0byte" => 'nullbyte',
    '<img src="x" onerror="alert(1)">' => '&lt;img src=&quot;x&quot; onerror=&quot;alert(1)&quot;&gt;'
];

foreach ($testInputs as $input => $expected) {
    $sanitized = sanitize($input);
    $passed = ($sanitized === $expected);
    echo "   Input: " . substr($input, 0, 20) . "... -> " . ($passed ? "PASS" : "FAIL") . "\n";
    if (!$passed) {
        echo "      Expected: $expected\n";
        echo "      Got: $sanitized\n";
    }
}

// Test 3: Password Validation
echo "\n3. Testing Password Validation...\n";

$passwords = [
    'short' => false,
    'toolong' => false,
    'ValidPass123' => true,
    'NoUppercase123' => false,
    'NOLOWERCASE123' => false,
    'NoNumbers' => false,
    'Valid123Pass' => true
];

foreach ($passwords as $password => $shouldBeValid) {
    $result = validatePasswordStrength($password);
    $passed = ($result['valid'] === $shouldBeValid);
    echo "   Password '$password': " . ($passed ? "PASS" : "FAIL") . "\n";
    if (!$passed) {
        echo "      Expected: " . ($shouldBeValid ? "valid" : "invalid") . "\n";
        echo "      Got: " . ($result['valid'] ? "valid" : "invalid") . "\n";
        echo "      Message: " . $result['message'] . "\n";
    }
}

// Test 4: Role Validation
echo "\n4. Testing Role Validation...\n";

$validRoles = ['admin', 'engineer', 'technician', 'staff'];
$invalidRoles = ['manager', 'user', 'supervisor', 'invalid'];

foreach ($validRoles as $role) {
    $isValid = in_array($role, $validRoles);
    echo "   Valid role '$role': " . ($isValid ? "PASS" : "FAIL") . "\n";
}

foreach ($invalidRoles as $role) {
    $isValid = in_array($role, $validRoles);
    echo "   Invalid role '$role': " . ($isValid ? "FAIL" : "PASS") . "\n";
}

// Test 5: Permission System
echo "\n5. Testing Permission System...\n";

// Mock user session
$_SESSION['user'] = [
    'id' => 1,
    'role' => 'technician',
    'permissions' => ['view_devices', 'manage_maintenance']
];

// Test permission checks
$permissionTests = [
    'view_devices' => true,
    'manage_maintenance' => true,
    'manage_users' => false,
    'manage_hospitals' => false
];

foreach ($permissionTests as $permission => $expected) {
    $hasPermission = hasPermission($permission, false); // Disable admin bypass
    $passed = ($hasPermission === $expected);
    echo "   Permission '$permission': " . ($passed ? "PASS" : "FAIL") . "\n";
}

// Test admin bypass
$_SESSION['user']['role'] = 'admin';
$hasAdminPermission = hasPermission('manage_users', true); // Enable admin bypass
echo "   Admin bypass: " . ($hasAdminPermission ? "PASS" : "FAIL") . "\n";

// Test 6: Database Sanitization
echo "\n6. Testing Database Sanitization...\n";

$dbInputs = [
    '<script>alert("xss")</script>' => 'scriptalert(xss)/script',
    'Normal text' => 'Normal text',
    'Text with "quotes" and \'apostrophes\'' => 'Text with quotes and apostrophes',
    '<img src="x">' => 'img src=x'
];

foreach ($dbInputs as $input => $expected) {
    $sanitized = sanitizeForDatabase($input);
    $passed = ($sanitized === $expected);
    echo "   DB Input: " . substr($input, 0, 20) . "... -> " . ($passed ? "PASS" : "FAIL") . "\n";
    if (!$passed) {
        echo "      Expected: $expected\n";
        echo "      Got: $sanitized\n";
    }
}

echo "\n=== Security Fixes Test Complete ===\n";
