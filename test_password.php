<?php
/**
 * Password Test Script
 * This script tests password hashing and verification
 */

// Test password hashing
$password = 'admin123';
$hash = password_hash($password, PASSWORD_DEFAULT);

echo "<h2>Password Test Results</h2>";
echo "<p><strong>Original Password:</strong> " . htmlspecialchars($password) . "</p>";
echo "<p><strong>Generated Hash:</strong> " . htmlspecialchars($hash) . "</p>";

// Test verification
$verify1 = password_verify($password, $hash);
echo "<p><strong>Verification Test 1:</strong> " . ($verify1 ? 'PASS' : 'FAIL') . "</p>";

// Test with the hash from schema.sql
$oldHash = '$2y$10$8zf0bvFUxHC8Wl7LQ1M3leZRqQBiQQx5uNDpP/NLqD0Pu5GXEfEju';
$verify2 = password_verify($password, $oldHash);
echo "<p><strong>Verification Test 2 (old hash):</strong> " . ($verify2 ? 'PASS' : 'FAIL') . "</p>";

// Test different passwords
$testPasswords = ['admin123', 'Admin123', 'admin', 'password'];
echo "<h3>Testing Different Passwords with Old Hash:</h3>";
foreach ($testPasswords as $testPass) {
    $result = password_verify($testPass, $oldHash);
    echo "<p><strong>Password '$testPass':</strong> " . ($result ? 'PASS' : 'FAIL') . "</p>";
}

// Generate a new hash for admin123
$newHash = password_hash('admin123', PASSWORD_DEFAULT);
echo "<h3>New Hash for admin123:</h3>";
echo "<p>" . htmlspecialchars($newHash) . "</p>";
?>
