<?php
/**
 * Login Diagnosis Script
 * This script helps diagnose login issues
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Login Diagnosis Tool</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; font-weight: bold; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 3px; }
</style>";

// Test 1: Database Connection
echo "<div class='section'>";
echo "<h2>1. Database Connection Test</h2>";
try {
    require_once 'config/database.php';
    if (isset($pdo)) {
        echo "<p class='success'>✓ Database connection successful</p>";
        
        // Test query
        $stmt = $pdo->query("SELECT 1");
        echo "<p class='success'>✓ Database query test passed</p>";
    } else {
        echo "<p class='error'>✗ PDO not initialized</p>";
        exit;
    }
} catch (Exception $e) {
    echo "<p class='error'>✗ Database connection failed: " . htmlspecialchars($e->getMessage()) . "</p>";
    exit;
}
echo "</div>";

// Test 2: Check Tables
echo "<div class='section'>";
echo "<h2>2. Database Tables Check</h2>";
try {
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $requiredTables = ['users', 'hospitals', 'departments', 'roles'];
    $missingTables = [];
    
    foreach ($requiredTables as $table) {
        if (in_array($table, $tables)) {
            echo "<p class='success'>✓ Table '$table' exists</p>";
        } else {
            echo "<p class='error'>✗ Table '$table' missing</p>";
            $missingTables[] = $table;
        }
    }
    
    if (!empty($missingTables)) {
        echo "<p class='error'>Missing tables detected. Please run the installation script.</p>";
        echo "<p><a href='database/install.php'>Run Installation</a></p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>✗ Table check failed: " . htmlspecialchars($e->getMessage()) . "</p>";
}
echo "</div>";

// Test 3: Check Admin User
echo "<div class='section'>";
echo "<h2>3. Admin User Check</h2>";
try {
    $stmt = $pdo->prepare("SELECT * FROM users WHERE username = ?");
    $stmt->execute(['admin']);
    $adminUser = $stmt->fetch();
    
    if ($adminUser) {
        echo "<p class='success'>✓ Admin user exists</p>";
        echo "<p><strong>User Details:</strong></p>";
        echo "<ul>";
        echo "<li>ID: " . $adminUser['id'] . "</li>";
        echo "<li>Username: " . htmlspecialchars($adminUser['username']) . "</li>";
        echo "<li>Email: " . htmlspecialchars($adminUser['email']) . "</li>";
        echo "<li>Full Name: " . htmlspecialchars($adminUser['full_name']) . "</li>";
        echo "<li>Role: " . htmlspecialchars($adminUser['role']) . "</li>";
        echo "<li>Status: " . htmlspecialchars($adminUser['status']) . "</li>";
        echo "<li>Hospital ID: " . ($adminUser['hospital_id'] ?? 'NULL') . "</li>";
        echo "<li>Language: " . htmlspecialchars($adminUser['language']) . "</li>";
        echo "</ul>";
        
        // Test password verification
        echo "<p><strong>Password Tests:</strong></p>";
        $testPasswords = ['admin123', 'Admin123', 'admin', 'password', '123456'];
        $validPassword = null;
        
        foreach ($testPasswords as $testPass) {
            $result = password_verify($testPass, $adminUser['password']);
            $status = $result ? 'success' : 'error';
            $icon = $result ? '✓' : '✗';
            echo "<p class='$status'>$icon Password '$testPass': " . ($result ? 'VALID' : 'INVALID') . "</p>";
            if ($result) {
                $validPassword = $testPass;
            }
        }
        
        if ($validPassword) {
            echo "<p class='success'>✓ Valid password found: '$validPassword'</p>";
        } else {
            echo "<p class='error'>✗ No valid password found from common passwords</p>";
        }
        
    } else {
        echo "<p class='error'>✗ Admin user not found</p>";
        
        // Check if any users exist
        $stmt = $pdo->query("SELECT COUNT(*) FROM users");
        $userCount = $stmt->fetchColumn();
        
        if ($userCount == 0) {
            echo "<p class='warning'>⚠ No users found in database</p>";
            echo "<p><a href='database/install.php'>Run Installation to create admin user</a></p>";
        } else {
            echo "<p class='info'>Found $userCount users in database</p>";
            
            // Show existing users
            $stmt = $pdo->query("SELECT username, email, role, status FROM users LIMIT 5");
            $users = $stmt->fetchAll();
            
            echo "<p><strong>Existing Users:</strong></p>";
            echo "<ul>";
            foreach ($users as $user) {
                echo "<li>" . htmlspecialchars($user['username']) . " (" . htmlspecialchars($user['role']) . ") - " . htmlspecialchars($user['status']) . "</li>";
            }
            echo "</ul>";
        }
    }
} catch (Exception $e) {
    echo "<p class='error'>✗ Admin user check failed: " . htmlspecialchars($e->getMessage()) . "</p>";
}
echo "</div>";

// Test 4: Authentication Function Test
echo "<div class='section'>";
echo "<h2>4. Authentication Function Test</h2>";
try {
    require_once 'includes/functions.php';
    require_once 'includes/auth.php';
    
    if (function_exists('authenticate')) {
        echo "<p class='success'>✓ Authentication function available</p>";
        
        if (isset($adminUser) && $adminUser) {
            // Test authentication with known password
            $testPasswords = ['admin123', 'Admin123', 'admin'];
            
            foreach ($testPasswords as $testPass) {
                // Start session for testing
                if (session_status() === PHP_SESSION_NONE) {
                    session_start();
                }
                
                $authResult = authenticate('admin', $testPass);
                $status = $authResult ? 'success' : 'error';
                $icon = $authResult ? '✓' : '✗';
                echo "<p class='$status'>$icon Authentication test with password '$testPass': " . ($authResult ? 'SUCCESS' : 'FAILED') . "</p>";
                
                if ($authResult) {
                    echo "<p class='success'>✓ Authentication successful! Session created.</p>";
                    break;
                }
            }
        }
    } else {
        echo "<p class='error'>✗ Authentication function not available</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>✗ Authentication test failed: " . htmlspecialchars($e->getMessage()) . "</p>";
}
echo "</div>";

// Test 5: Session Test
echo "<div class='section'>";
echo "<h2>5. Session Test</h2>";
if (session_status() === PHP_SESSION_ACTIVE) {
    echo "<p class='success'>✓ Session is active</p>";
    echo "<p>Session ID: " . session_id() . "</p>";
    
    if (isset($_SESSION['user'])) {
        echo "<p class='success'>✓ User session exists</p>";
        echo "<p>Logged in as: " . htmlspecialchars($_SESSION['user']['username']) . "</p>";
    } else {
        echo "<p class='info'>No user session found</p>";
    }
} else {
    echo "<p class='warning'>⚠ Session not active</p>";
}
echo "</div>";

// Test 6: File Permissions
echo "<div class='section'>";
echo "<h2>6. File Permissions Test</h2>";
$testDirs = ['uploads', 'uploads/qrcodes'];
foreach ($testDirs as $dir) {
    if (is_dir($dir)) {
        $writable = is_writable($dir);
        $status = $writable ? 'success' : 'warning';
        $icon = $writable ? '✓' : '⚠';
        echo "<p class='$status'>$icon Directory '$dir': " . ($writable ? 'Writable' : 'Read-only') . "</p>";
    } else {
        echo "<p class='error'>✗ Directory '$dir': Missing</p>";
    }
}
echo "</div>";

echo "<div class='section'>";
echo "<h2>Summary & Next Steps</h2>";
echo "<p><strong>If you can't login:</strong></p>";
echo "<ol>";
echo "<li>Make sure the database is properly installed by running <a href='database/install.php'>database/install.php</a></li>";
echo "<li>Use the default credentials: <strong>Username:</strong> admin, <strong>Password:</strong> admin123</li>";
echo "<li>Check that the admin user status is 'active'</li>";
echo "<li>Clear your browser cache and cookies</li>";
echo "<li>Try accessing the login page in an incognito/private window</li>";
echo "</ol>";
echo "</div>";
?>
