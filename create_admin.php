<?php
/**
 * Create Admin User Script
 * This script creates a default admin user if one doesn't exist
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Create Admin User</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; font-weight: bold; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
</style>";

try {
    // Include database configuration
    require_once 'config/database.php';
    
    if (!isset($pdo)) {
        throw new Exception("Database connection not available");
    }
    
    echo "<div class='section'>";
    echo "<h2>Database Connection</h2>";
    echo "<p class='success'>✓ Connected to database successfully</p>";
    echo "</div>";
    
    // Check if hospitals table exists and has data
    echo "<div class='section'>";
    echo "<h2>Hospital Check</h2>";
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM hospitals");
    $hospitalCount = $stmt->fetchColumn();
    
    if ($hospitalCount == 0) {
        echo "<p class='warning'>⚠ No hospitals found. Creating default hospital...</p>";
        
        $stmt = $pdo->prepare("
            INSERT INTO hospitals (name, address, city, country, phone, email)
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            'Main Hospital',
            '123 Main Street',
            'Main City',
            'Country',
            '+1234567890',
            '<EMAIL>'
        ]);
        
        $hospitalId = $pdo->lastInsertId();
        echo "<p class='success'>✓ Default hospital created (ID: $hospitalId)</p>";
    } else {
        $stmt = $pdo->query("SELECT id, name FROM hospitals LIMIT 1");
        $hospital = $stmt->fetch();
        $hospitalId = $hospital['id'];
        echo "<p class='success'>✓ Using existing hospital: " . htmlspecialchars($hospital['name']) . " (ID: $hospitalId)</p>";
    }
    echo "</div>";
    
    // Check if admin user exists
    echo "<div class='section'>";
    echo "<h2>Admin User Check</h2>";
    
    $stmt = $pdo->prepare("SELECT * FROM users WHERE username = ?");
    $stmt->execute(['admin']);
    $existingAdmin = $stmt->fetch();
    
    if ($existingAdmin) {
        echo "<p class='warning'>⚠ Admin user already exists</p>";
        echo "<p><strong>Existing Admin Details:</strong></p>";
        echo "<ul>";
        echo "<li>Username: " . htmlspecialchars($existingAdmin['username']) . "</li>";
        echo "<li>Email: " . htmlspecialchars($existingAdmin['email']) . "</li>";
        echo "<li>Status: " . htmlspecialchars($existingAdmin['status']) . "</li>";
        echo "<li>Hospital ID: " . ($existingAdmin['hospital_id'] ?? 'NULL') . "</li>";
        echo "</ul>";
        
        // Test password
        $testPassword = 'admin123';
        $passwordValid = password_verify($testPassword, $existingAdmin['password']);
        
        if ($passwordValid) {
            echo "<p class='success'>✓ Default password 'admin123' is valid</p>";
        } else {
            echo "<p class='error'>✗ Default password 'admin123' is not valid</p>";
            echo "<p class='info'>Updating password to 'admin123'...</p>";
            
            $newHash = password_hash($testPassword, PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE id = ?");
            $result = $stmt->execute([$newHash, $existingAdmin['id']]);
            
            if ($result) {
                echo "<p class='success'>✓ Password updated successfully</p>";
            } else {
                echo "<p class='error'>✗ Failed to update password</p>";
            }
        }
        
        // Ensure user is active
        if ($existingAdmin['status'] !== 'active') {
            echo "<p class='warning'>⚠ Admin user is not active. Activating...</p>";
            $stmt = $pdo->prepare("UPDATE users SET status = 'active' WHERE id = ?");
            $result = $stmt->execute([$existingAdmin['id']]);
            
            if ($result) {
                echo "<p class='success'>✓ Admin user activated</p>";
            } else {
                echo "<p class='error'>✗ Failed to activate admin user</p>";
            }
        }
        
        // Ensure hospital_id is set
        if (!$existingAdmin['hospital_id']) {
            echo "<p class='warning'>⚠ Admin user has no hospital assigned. Assigning...</p>";
            $stmt = $pdo->prepare("UPDATE users SET hospital_id = ? WHERE id = ?");
            $result = $stmt->execute([$hospitalId, $existingAdmin['id']]);
            
            if ($result) {
                echo "<p class='success'>✓ Hospital assigned to admin user</p>";
            } else {
                echo "<p class='error'>✗ Failed to assign hospital to admin user</p>";
            }
        }
        
    } else {
        echo "<p class='info'>Creating new admin user...</p>";
        
        $adminPermissions = json_encode([
            'view_dashboard', 'manage_users', 'manage_hospitals', 'manage_departments',
            'manage_devices', 'manage_maintenance', 'manage_tickets', 'view_reports',
            'export_data', 'manage_roles', 'view_roles'
        ]);
        
        $stmt = $pdo->prepare("
            INSERT INTO users (
                username, password, email, full_name, role, permissions, 
                hospital_id, language, status
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $result = $stmt->execute([
            'admin',
            password_hash('admin123', PASSWORD_DEFAULT),
            '<EMAIL>',
            'System Administrator',
            'admin',
            $adminPermissions,
            $hospitalId,
            'en',
            'active'
        ]);
        
        if ($result) {
            $adminId = $pdo->lastInsertId();
            echo "<p class='success'>✓ Admin user created successfully (ID: $adminId)</p>";
        } else {
            echo "<p class='error'>✗ Failed to create admin user</p>";
        }
    }
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>Login Credentials</h2>";
    echo "<p class='success'><strong>You can now login with:</strong></p>";
    echo "<ul>";
    echo "<li><strong>Username:</strong> admin</li>";
    echo "<li><strong>Password:</strong> admin123</li>";
    echo "</ul>";
    echo "<p><a href='login.php'>Go to Login Page</a></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='section'>";
    echo "<p class='error'>✗ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>Please check your database configuration and ensure the database is properly set up.</p>";
    echo "<p><a href='database/install.php'>Run Full Installation</a></p>";
    echo "</div>";
}
?>
