<?php
/**
 * Devices Controller
 *
 * This file handles device-related operations.
 */

// Check if the user is logged in
requireLogin();

// Get the current user
$currentUser = getCurrentUser();
$userHospitalId = $currentUser['hospital_id'];

// Handle actions
$action = isset($url[1]) ? $url[1] : 'index';
$deviceId = isset($url[2]) ? (int)$url[2] : 0;

switch ($action) {
    case 'index':
        // Check if the user has permission to view devices
        if (!hasPermission('view_devices') && !hasPermission('manage_devices')) {
            setFlashMessage('error', __('access_denied'));
            redirect('dashboard');
        }

        // Get filter parameters
        $hospitalId = isset($_GET['hospital_id']) ? (int)$_GET['hospital_id'] : $userHospitalId;
        $departmentId = isset($_GET['department_id']) ? (int)$_GET['department_id'] : null;
        $status = isset($_GET['status']) ? $_GET['status'] : null;
        $search = isset($_GET['search']) ? $_GET['search'] : null;

        // Get devices based on filters
        if ($search) {
            $devices = $deviceModel->search($search, $hospitalId);
        } elseif ($status) {
            $devices = $deviceModel->getByStatus($status, $hospitalId, $departmentId);
        } else {
            $devices = $deviceModel->getAll($hospitalId, $departmentId);
        }

        // Get hospitals and departments for the filters
        if (hasPermission('manage_hospitals')) {
            $hospitals = $hospitalModel->getAll();
        } elseif ($userHospitalId) {
            $hospitals = [$hospitalModel->getById($userHospitalId)];
        } else {
            $hospitals = [];
        }

        if ($hospitalId) {
            $departments = $departmentModel->getByHospital($hospitalId);
        } else {
            $departments = [];
        }

        // Include the devices index view
        include 'views/devices/index.php';
        break;

    case 'create':
        // Check if the user has permission to manage devices
        requirePermission('manage_devices');

        // Initialize device data and errors
        $deviceData = [
            'hospital_id' => $userHospitalId,
            'department_id' => null,
            'name' => '',
            'model' => '',
            'serial_number' => '',
            'manufacturer' => '',
            'category' => 'Other',
            'purchase_date' => date('Y-m-d'),
            'warranty_expiry' => date('Y-m-d', strtotime('+1 year')),
            'status' => 'operational',
            'location' => '',
            'maintenance_interval' => '',
            'notes' => ''
        ];
        $errors = [];

        // Check for form data from session (after validation errors)
        if (isset($_SESSION['form_data'])) {
            $deviceData = array_merge($deviceData, $_SESSION['form_data']);
            unset($_SESSION['form_data']);
        }

        // Check for form errors from session
        if (isset($_SESSION['form_errors'])) {
            $errors = $_SESSION['form_errors'];
            unset($_SESSION['form_errors']);
        }

        // Get hospitals for the dropdown
        if (hasPermission('manage_hospitals')) {
            $hospitals = $hospitalModel->getAll();
        } elseif ($userHospitalId) {
            $hospitals = [$hospitalModel->getById($userHospitalId)];
        } else {
            $hospitals = [];
        }

        // Get departments for the dropdown
        if ($deviceData['hospital_id']) {
            $departments = $departmentModel->getByHospital($deviceData['hospital_id']);
        } else {
            $departments = [];
        }

        // Include the devices create view
        include 'views/devices/create.php';
        break;

    case 'store':
        // Check if the user has permission to manage devices
        requirePermission('manage_devices');

        // Only handle POST requests
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            redirect('devices/create');
        }

        // Validate CSRF token
        if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
            setFlashMessage('error', __('invalid_csrf_token'));
            redirect('devices/create');
        }

        // Sanitize and prepare device data
        $deviceData = [
            'hospital_id' => (int)($_POST['hospital_id'] ?? $userHospitalId),
            'department_id' => (int)($_POST['department_id'] ?? 0),
            'name' => sanitize($_POST['name'] ?? ''),
            'model' => sanitize($_POST['model'] ?? ''),
            'serial_number' => sanitize($_POST['serial_number'] ?? ''),
            'manufacturer' => sanitize($_POST['manufacturer'] ?? ''),
            'category' => sanitize($_POST['category'] ?? 'Other'),
            'purchase_date' => $_POST['purchase_date'] ?? date('Y-m-d'),
            'warranty_expiry' => $_POST['warranty_expiry'] ?? date('Y-m-d', strtotime('+1 year')),
            'status' => $_POST['status'] ?? 'operational',
            'location' => sanitize($_POST['location'] ?? ''),
            'maintenance_interval' => !empty($_POST['maintenance_interval']) ? (int)$_POST['maintenance_interval'] : null,
            'notes' => sanitize($_POST['notes'] ?? '')
        ];

        // Validate required fields
        $errors = [];

        if (empty($deviceData['hospital_id'])) {
            $errors['hospital_id'] = __('required_field');
        }

        if (empty($deviceData['department_id'])) {
            $errors['department_id'] = __('required_field');
        }

        if (empty($deviceData['name'])) {
            $errors['name'] = __('required_field');
        }

        if (empty($deviceData['model'])) {
            $errors['model'] = __('required_field');
        }

        if (empty($deviceData['serial_number'])) {
            $errors['serial_number'] = __('required_field');
        } else {
            // Check for duplicate serial number
            $existingDevice = $deviceModel->getBySerialNumber($deviceData['serial_number']);
            if ($existingDevice) {
                $errors['serial_number'] = __('serial_number_taken');
            }
        }

        if (empty($deviceData['manufacturer'])) {
            $errors['manufacturer'] = __('required_field');
        }

        if (empty($deviceData['category'])) {
            $errors['category'] = __('required_field');
        }

        if (empty($deviceData['purchase_date'])) {
            $errors['purchase_date'] = __('required_field');
        }

        if (empty($deviceData['warranty_expiry'])) {
            $errors['warranty_expiry'] = __('required_field');
        }

        // If validation passes, create the device
        if (empty($errors)) {
            try {
                $deviceId = $deviceModel->create($deviceData);

                if ($deviceId) {
                    // Log the action
                    logAction('create_device', 'Created device: ' . $deviceData['name'] . ' (ID: ' . $deviceId . ')');

                    // Clear any stored form data
                    unset($_SESSION['form_data'], $_SESSION['form_errors']);

                    // Set success message and redirect
                    setFlashMessage('success', __('device') . ' "' . $deviceData['name'] . '" ' . __('created_successfully'));
                    redirect('devices');
                } else {
                    $errors['general'] = __('create_failed') . ' - Database operation failed';
                }
            } catch (Exception $e) {
                error_log("Device creation error: " . $e->getMessage());
                $errors['general'] = __('create_failed') . ' - ' . $e->getMessage();
            }
        }

        // If there are errors, store them in session and redirect back
        if (!empty($errors)) {
            $_SESSION['form_errors'] = $errors;
            $_SESSION['form_data'] = $deviceData;
            redirect('devices/create');
        }
        break;

    case 'edit':
        // Check if the user has permission to manage devices
        requirePermission('manage_devices');

        // Get the device
        $device = $deviceModel->getById($deviceId);

        // Check if the device exists
        if (!$device) {
            setFlashMessage('error', __('not_found'));
            redirect('devices');
        }

        // Check if the user has permission to edit this device
        if ($userHospitalId && $device['hospital_id'] != $userHospitalId && !hasPermission('manage_hospitals')) {
            setFlashMessage('error', __('access_denied'));
            redirect('devices');
        }

        // Handle form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Validate CSRF token
            if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
                setFlashMessage('error', __('invalid_csrf_token'));
                redirect('devices/edit/' . $deviceId);
            }

            $deviceData = [
                'hospital_id' => $_POST['hospital_id'] ?? $device['hospital_id'],
                'department_id' => $_POST['department_id'] ?? $device['department_id'],
                'name' => $_POST['name'] ?? $device['name'],
                'model' => $_POST['model'] ?? $device['model'],
                'serial_number' => $_POST['serial_number'] ?? $device['serial_number'],
                'manufacturer' => $_POST['manufacturer'] ?? $device['manufacturer'],
                'category' => $_POST['category'] ?? $device['category'],
                'purchase_date' => $_POST['purchase_date'] ?? $device['purchase_date'],
                'warranty_expiry' => $_POST['warranty_expiry'] ?? $device['warranty_expiry'],
                'status' => $_POST['status'] ?? $device['status'],
                'location' => $_POST['location'] ?? $device['location'],
                'maintenance_interval' => $_POST['maintenance_interval'] ?? $device['maintenance_interval'],
                'notes' => $_POST['notes'] ?? $device['notes'],
                'regenerate_qr' => isset($_POST['regenerate_qr']) && $_POST['regenerate_qr'] === 'yes'
            ];

            // Validate data
            $errors = [];

            if (empty($deviceData['hospital_id'])) {
                $errors['hospital_id'] = __('required_field');
            }

            if (empty($deviceData['department_id'])) {
                $errors['department_id'] = __('required_field');
            }

            if (empty($deviceData['name'])) {
                $errors['name'] = __('required_field');
            }

            if (empty($deviceData['serial_number'])) {
                $errors['serial_number'] = __('required_field');
            } elseif ($deviceData['serial_number'] !== $device['serial_number'] && $deviceModel->getBySerialNumber($deviceData['serial_number'])) {
                $errors['serial_number'] = __('serial_number_taken');
            }

            // If no errors, update the device
            if (empty($errors)) {
                $result = $deviceModel->update($deviceId, $deviceData);

                if ($result) {
                    // Log the action
                    logAction('update_device', 'Updated device: ' . $deviceData['name']);

                    // Set flash message
                    setFlashMessage('success', __('updated_successfully', [__('device')]));

                    // Redirect to devices index
                    redirect('devices');
                } else {
                    $errors['general'] = __('update_failed');
                }
            }
        } else {
            $deviceData = $device;
            $errors = [];
        }

        // Get hospitals for the dropdown
        if (hasPermission('manage_hospitals')) {
            $hospitals = $hospitalModel->getAll();
        } elseif ($userHospitalId) {
            $hospitals = [$hospitalModel->getById($userHospitalId)];
        } else {
            $hospitals = [];
        }

        // Get departments for the dropdown
        if ($deviceData['hospital_id']) {
            $departments = $departmentModel->getByHospital($deviceData['hospital_id']);
        } else {
            $departments = [];
        }

        // Include the devices edit view
        include 'views/devices/edit.php';
        break;

    case 'delete':
        // Check if the user has permission to manage devices
        requirePermission('manage_devices');

        // Get the device
        $device = $deviceModel->getById($deviceId);

        // Check if the device exists
        if (!$device) {
            setFlashMessage('error', __('not_found'));
            redirect('devices');
        }

        // Check if the user has permission to delete this device
        if ($userHospitalId && $device['hospital_id'] != $userHospitalId && !hasPermission('manage_hospitals')) {
            setFlashMessage('error', __('access_denied'));
            redirect('devices');
        }

        // Handle form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['confirm']) && $_POST['confirm'] === 'yes') {
            $result = $deviceModel->delete($deviceId);

            if ($result) {
                // Log the action
                logAction('delete_device', 'Deleted device: ' . $device['name']);

                // Set flash message
                setFlashMessage('success', __('deleted_successfully', [__('device')]));
            } else {
                setFlashMessage('error', __('delete_failed'));
            }

            // Redirect to devices index
            redirect('devices');
        }

        // Include the devices delete view
        include 'views/devices/delete.php';
        break;

    case 'view':
        // Check if the user has permission to view devices
        if (!hasPermission('view_devices') && !hasPermission('manage_devices')) {
            setFlashMessage('error', __('access_denied'));
            redirect('dashboard');
        }

        // Get the device
        $device = $deviceModel->getById($deviceId);

        // Check if the device exists
        if (!$device) {
            setFlashMessage('error', __('not_found'));
            redirect('devices');
        }

        // Check if the user has permission to view this device
        if ($userHospitalId && $device['hospital_id'] != $userHospitalId && !hasPermission('manage_hospitals')) {
            setFlashMessage('error', __('access_denied'));
            redirect('devices');
        }

        // Get maintenance schedules for this device
        $maintenanceSchedules = $maintenanceModel->getAllSchedules($deviceId);

        // Get maintenance logs for this device
        $maintenanceLogs = $maintenanceModel->getAllLogs(null, $deviceId);

        // Get tickets for this device
        $tickets = $ticketModel->getAll($deviceId);

        // Include the devices view
        include 'views/devices/view.php';
        break;

    case 'qrcode':
        // Check if the user has permission to view devices
        if (!hasPermission('view_devices') && !hasPermission('manage_devices')) {
            setFlashMessage('error', __('access_denied'));
            redirect('dashboard');
        }

        // Get the device
        $device = $deviceModel->getById($deviceId);

        // Check if the device exists
        if (!$device) {
            setFlashMessage('error', __('not_found'));
            redirect('devices');
        }

        // Check if the user has permission to view this device
        if ($userHospitalId && $device['hospital_id'] != $userHospitalId && !hasPermission('manage_hospitals')) {
            setFlashMessage('error', __('access_denied'));
            redirect('devices');
        }

        // Include the devices QR code view
        include 'views/devices/qrcode.php';
        break;

    default:
        // 404 Not Found
        header("HTTP/1.0 404 Not Found");
        include 'views/404.php';
        break;
}
