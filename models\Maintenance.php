<?php
/**
 * Maintenance Model
 * 
 * This class handles maintenance-related database operations.
 */
class Maintenance {
    private $pdo;
    
    /**
     * Constructor
     * 
     * @param PDO $pdo The PDO instance
     */
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }

    /**
     * Get all maintenance records (schedules and logs combined)
     * This method provides a unified view of all maintenance activities
     *
     * @param int $deviceId Optional device ID to filter records
     * @param int $hospitalId Optional hospital ID to filter records
     * @return array The maintenance records
     */
    public function getAll($deviceId = null, $hospitalId = null) {
        try {
            // Get both schedules and logs in a unified format
            $schedules = $this->getAllSchedules($deviceId, $hospitalId);
            $logs = $this->getAllLogs(null, $deviceId, $hospitalId);

            // Combine and format the results
            $allRecords = [];

            // Add schedules with type indicator
            foreach ($schedules as $schedule) {
                $allRecords[] = array_merge($schedule, [
                    'type' => 'schedule',
                    'date' => $schedule['scheduled_date'],
                    'title' => $schedule['title'],
                    'description' => $schedule['description'] ?? '',
                    'status' => $schedule['status']
                ]);
            }

            // Add logs with type indicator
            foreach ($logs as $log) {
                $allRecords[] = array_merge($log, [
                    'type' => 'log',
                    'date' => $log['performed_date'],
                    'title' => $log['title'] ?? 'Maintenance Log',
                    'description' => $log['notes'] ?? '',
                    'status' => $log['status']
                ]);
            }

            // Sort by date (most recent first)
            usort($allRecords, function($a, $b) {
                return strtotime($b['date']) - strtotime($a['date']);
            });

            return $allRecords;
        } catch (Exception $e) {
            error_log("Get All Maintenance Records Error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get a maintenance record by ID
     * This method can retrieve either a schedule or log by ID
     *
     * @param int $id The record ID
     * @param string $type The type of record ('schedule' or 'log'), auto-detect if null
     * @return array|bool The record or false if not found
     */
    public function getById($id, $type = null) {
        try {
            // If type is specified, use the appropriate method
            if ($type === 'schedule') {
                return $this->getScheduleById($id);
            } elseif ($type === 'log') {
                return $this->getLogById($id);
            }

            // Auto-detect type by trying both
            $schedule = $this->getScheduleById($id);
            if ($schedule) {
                return array_merge($schedule, ['type' => 'schedule']);
            }

            $log = $this->getLogById($id);
            if ($log) {
                return array_merge($log, ['type' => 'log']);
            }

            return false;
        } catch (Exception $e) {
            error_log("Get Maintenance Record By ID Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Create a new maintenance record
     * This method can create either a schedule or log based on the data provided
     *
     * @param array $data The record data
     * @return int|bool The record ID if successful, false otherwise
     */
    public function create($data) {
        try {
            // Determine type based on data structure
            if (isset($data['type'])) {
                $type = $data['type'];
            } elseif (isset($data['scheduled_date'])) {
                $type = 'schedule';
            } elseif (isset($data['performed_date'])) {
                $type = 'log';
            } else {
                // Default to schedule if ambiguous
                $type = 'schedule';
            }

            if ($type === 'schedule') {
                return $this->createSchedule($data);
            } else {
                return $this->createLog($data);
            }
        } catch (Exception $e) {
            error_log("Create Maintenance Record Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Update a maintenance record
     * This method can update either a schedule or log
     *
     * @param int $id The record ID
     * @param array $data The record data
     * @return bool True if successful, false otherwise
     */
    public function update($id, $data) {
        try {
            // Determine type based on data structure or explicit type
            if (isset($data['type'])) {
                $type = $data['type'];
            } elseif (isset($data['scheduled_date'])) {
                $type = 'schedule';
            } elseif (isset($data['performed_date'])) {
                $type = 'log';
            } else {
                // Try to auto-detect by checking which record exists
                $schedule = $this->getScheduleById($id);
                $type = $schedule ? 'schedule' : 'log';
            }

            if ($type === 'schedule') {
                return $this->updateSchedule($id, $data);
            } else {
                return $this->updateLog($id, $data);
            }
        } catch (Exception $e) {
            error_log("Update Maintenance Record Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Delete a maintenance record
     * This method can delete either a schedule or log
     *
     * @param int $id The record ID
     * @param string $type The type of record ('schedule' or 'log'), auto-detect if null
     * @return bool True if successful, false otherwise
     */
    public function delete($id, $type = null) {
        try {
            // If type is specified, use the appropriate method
            if ($type === 'schedule') {
                return $this->deleteSchedule($id);
            } elseif ($type === 'log') {
                return $this->deleteLog($id);
            }

            // Auto-detect type by checking which record exists
            $schedule = $this->getScheduleById($id);
            if ($schedule) {
                return $this->deleteSchedule($id);
            }

            $log = $this->getLogById($id);
            if ($log) {
                return $this->deleteLog($id);
            }

            // Record not found
            return false;
        } catch (Exception $e) {
            error_log("Delete Maintenance Record Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get all maintenance schedules
     * 
     * @param int $deviceId Optional device ID to filter schedules
     * @param int $hospitalId Optional hospital ID to filter schedules
     * @return array The maintenance schedules
     */
    public function getAllSchedules($deviceId = null, $hospitalId = null) {
        try {
            $sql = "
                SELECT ms.*, d.name AS device_name, d.serial_number,
                       h.name AS hospital_name, dp.name AS department_name,
                       u.full_name AS created_by_name
                FROM maintenance_schedules ms
                JOIN devices d ON ms.device_id = d.id
                JOIN hospitals h ON d.hospital_id = h.id
                JOIN departments dp ON d.department_id = dp.id
                JOIN users u ON ms.created_by = u.id
            ";
            $params = [];
            $where = [];
            
            if ($deviceId) {
                $where[] = "ms.device_id = ?";
                $params[] = $deviceId;
            }
            
            if ($hospitalId) {
                $where[] = "d.hospital_id = ?";
                $params[] = $hospitalId;
            }
            
            if (!empty($where)) {
                $sql .= " WHERE " . implode(" AND ", $where);
            }
            
            $sql .= " ORDER BY ms.scheduled_date DESC";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Get All Maintenance Schedules Error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get a maintenance schedule by ID
     * 
     * @param int $id The schedule ID
     * @return array|bool The schedule or false if not found
     */
    public function getScheduleById($id) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT ms.*, d.name AS device_name, d.serial_number,
                       h.name AS hospital_name, dp.name AS department_name,
                       u.full_name AS created_by_name
                FROM maintenance_schedules ms
                JOIN devices d ON ms.device_id = d.id
                JOIN hospitals h ON d.hospital_id = h.id
                JOIN departments dp ON d.department_id = dp.id
                JOIN users u ON ms.created_by = u.id
                WHERE ms.id = ?
            ");
            
            $stmt->execute([$id]);
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("Get Maintenance Schedule By ID Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Create a new maintenance schedule
     * 
     * @param array $data The schedule data
     * @return int|bool The schedule ID if successful, false otherwise
     */
    public function createSchedule($data) {
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO maintenance_schedules (
                    device_id, title, description, scheduled_date,
                    frequency, status, created_by
                ) VALUES (
                    ?, ?, ?, ?, ?, ?, ?
                )
            ");
            
            $stmt->execute([
                $data['device_id'],
                $data['title'],
                $data['description'],
                $data['scheduled_date'],
                $data['frequency'],
                $data['status'] ?? 'scheduled',
                $data['created_by']
            ]);
            
            $scheduleId = $this->pdo->lastInsertId();
            
            // Create notification (if function exists)
            if (function_exists('createMaintenanceNotification')) {
                createMaintenanceNotification(
                    $data['device_id'],
                    $data['title'],
                    $data['scheduled_date']
                );
            }
            
            return $scheduleId;
        } catch (PDOException $e) {
            error_log("Create Maintenance Schedule Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update a maintenance schedule
     * 
     * @param int $id The schedule ID
     * @param array $data The schedule data
     * @return bool True if successful, false otherwise
     */
    public function updateSchedule($id, $data) {
        try {
            $stmt = $this->pdo->prepare("
                UPDATE maintenance_schedules SET
                    device_id = ?,
                    title = ?,
                    description = ?,
                    scheduled_date = ?,
                    frequency = ?,
                    status = ?,
                    updated_at = NOW()
                WHERE id = ?
            ");
            
            return $stmt->execute([
                $data['device_id'],
                $data['title'],
                $data['description'],
                $data['scheduled_date'],
                $data['frequency'],
                $data['status'],
                $id
            ]);
        } catch (PDOException $e) {
            error_log("Update Maintenance Schedule Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete a maintenance schedule
     * 
     * @param int $id The schedule ID
     * @return bool True if successful, false otherwise
     */
    public function deleteSchedule($id) {
        try {
            // Check if there are any maintenance logs for this schedule
            $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM maintenance_logs WHERE maintenance_schedule_id = ?");
            $stmt->execute([$id]);
            $logCount = $stmt->fetchColumn();
            
            if ($logCount > 0) {
                // Cannot delete a schedule with logs
                return false;
            }
            
            $stmt = $this->pdo->prepare("DELETE FROM maintenance_schedules WHERE id = ?");
            return $stmt->execute([$id]);
        } catch (PDOException $e) {
            error_log("Delete Maintenance Schedule Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get all maintenance logs
     * 
     * @param int $scheduleId Optional schedule ID to filter logs
     * @param int $deviceId Optional device ID to filter logs
     * @param int $hospitalId Optional hospital ID to filter logs
     * @return array The maintenance logs
     */
    public function getAllLogs($scheduleId = null, $deviceId = null, $hospitalId = null) {
        try {
            $sql = "
                SELECT ml.*, ms.title, d.name AS device_name, d.serial_number,
                       h.name AS hospital_name, dp.name AS department_name,
                       u.full_name AS performed_by_name
                FROM maintenance_logs ml
                LEFT JOIN maintenance_schedules ms ON ml.maintenance_schedule_id = ms.id
                JOIN devices d ON ml.device_id = d.id
                JOIN hospitals h ON d.hospital_id = h.id
                JOIN departments dp ON d.department_id = dp.id
                JOIN users u ON ml.performed_by = u.id
            ";
            $params = [];
            $where = [];
            
            if ($scheduleId) {
                $where[] = "ml.maintenance_schedule_id = ?";
                $params[] = $scheduleId;
            }
            
            if ($deviceId) {
                $where[] = "ml.device_id = ?";
                $params[] = $deviceId;
            }
            
            if ($hospitalId) {
                $where[] = "d.hospital_id = ?";
                $params[] = $hospitalId;
            }
            
            if (!empty($where)) {
                $sql .= " WHERE " . implode(" AND ", $where);
            }
            
            $sql .= " ORDER BY ml.performed_date DESC";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Get All Maintenance Logs Error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get a maintenance log by ID
     * 
     * @param int $id The log ID
     * @return array|bool The log or false if not found
     */
    public function getLogById($id) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT ml.*, ms.title, d.name AS device_name, d.serial_number,
                       h.name AS hospital_name, dp.name AS department_name,
                       u.full_name AS performed_by_name
                FROM maintenance_logs ml
                LEFT JOIN maintenance_schedules ms ON ml.maintenance_schedule_id = ms.id
                JOIN devices d ON ml.device_id = d.id
                JOIN hospitals h ON d.hospital_id = h.id
                JOIN departments dp ON d.department_id = dp.id
                JOIN users u ON ml.performed_by = u.id
                WHERE ml.id = ?
            ");
            
            $stmt->execute([$id]);
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("Get Maintenance Log By ID Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Create a new maintenance log
     * 
     * @param array $data The log data
     * @return int|bool The log ID if successful, false otherwise
     */
    public function createLog($data) {
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO maintenance_logs (
                    maintenance_schedule_id, device_id, performed_by,
                    performed_date, notes, status
                ) VALUES (
                    ?, ?, ?, ?, ?, ?
                )
            ");
            
            $stmt->execute([
                $data['maintenance_schedule_id'] ?? null,
                $data['device_id'],
                $data['performed_by'],
                $data['performed_date'],
                $data['notes'],
                $data['status']
            ]);
            
            $logId = $this->pdo->lastInsertId();
            
            // Update the schedule status if this log is for a schedule
            if (!empty($data['maintenance_schedule_id'])) {
                $stmt = $this->pdo->prepare("
                    UPDATE maintenance_schedules SET
                        status = 'completed',
                        updated_at = NOW()
                    WHERE id = ?
                ");
                
                $stmt->execute([$data['maintenance_schedule_id']]);
                
                // If the schedule is recurring, create the next schedule
                $stmt = $this->pdo->prepare("
                    SELECT * FROM maintenance_schedules
                    WHERE id = ?
                ");
                
                $stmt->execute([$data['maintenance_schedule_id']]);
                $schedule = $stmt->fetch();
                
                if ($schedule && $schedule['frequency'] !== 'once') {
                    $nextDate = $this->calculateNextScheduleDate(
                        $schedule['scheduled_date'],
                        $schedule['frequency']
                    );
                    
                    $this->createSchedule([
                        'device_id' => $schedule['device_id'],
                        'title' => $schedule['title'],
                        'description' => $schedule['description'],
                        'scheduled_date' => $nextDate,
                        'frequency' => $schedule['frequency'],
                        'status' => 'scheduled',
                        'created_by' => $schedule['created_by']
                    ]);
                }
            }
            
            // Update device status if maintenance is completed
            if ($data['status'] === 'completed') {
                try {
                    $deviceModel = new Device($this->pdo);
                    $deviceModel->updateStatus($data['device_id'], 'operational');
                } catch (Exception $e) {
                    error_log("Device status update error: " . $e->getMessage());
                }
            }
            
            return $logId;
        } catch (PDOException $e) {
            error_log("Create Maintenance Log Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update a maintenance log
     * 
     * @param int $id The log ID
     * @param array $data The log data
     * @return bool True if successful, false otherwise
     */
    public function updateLog($id, $data) {
        try {
            $stmt = $this->pdo->prepare("
                UPDATE maintenance_logs SET
                    maintenance_schedule_id = ?,
                    device_id = ?,
                    performed_by = ?,
                    performed_date = ?,
                    notes = ?,
                    status = ?,
                    updated_at = NOW()
                WHERE id = ?
            ");
            
            $result = $stmt->execute([
                $data['maintenance_schedule_id'] ?? null,
                $data['device_id'],
                $data['performed_by'],
                $data['performed_date'],
                $data['notes'],
                $data['status'],
                $id
            ]);
            
            // Update device status if maintenance is completed
            if ($result && $data['status'] === 'completed') {
                try {
                    $deviceModel = new Device($this->pdo);
                    $deviceModel->updateStatus($data['device_id'], 'operational');
                } catch (Exception $e) {
                    error_log("Device status update error: " . $e->getMessage());
                }
            }
            
            return $result;
        } catch (PDOException $e) {
            error_log("Update Maintenance Log Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete a maintenance log
     * 
     * @param int $id The log ID
     * @return bool True if successful, false otherwise
     */
    public function deleteLog($id) {
        try {
            $stmt = $this->pdo->prepare("DELETE FROM maintenance_logs WHERE id = ?");
            return $stmt->execute([$id]);
        } catch (PDOException $e) {
            error_log("Delete Maintenance Log Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get upcoming maintenance schedules
     * 
     * @param int $days The number of days to look ahead
     * @param int $hospitalId Optional hospital ID to filter schedules
     * @return array The upcoming schedules
     */
    public function getUpcomingSchedules($days = 7, $hospitalId = null) {
        try {
            $sql = "
                SELECT ms.*, d.name AS device_name, d.serial_number,
                       h.name AS hospital_name, dp.name AS department_name,
                       u.full_name AS created_by_name
                FROM maintenance_schedules ms
                JOIN devices d ON ms.device_id = d.id
                JOIN hospitals h ON d.hospital_id = h.id
                JOIN departments dp ON d.department_id = dp.id
                JOIN users u ON ms.created_by = u.id
                WHERE ms.status = 'scheduled'
                AND ms.scheduled_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL ? DAY)
            ";
            $params = [$days];
            
            if ($hospitalId) {
                $sql .= " AND d.hospital_id = ?";
                $params[] = $hospitalId;
            }
            
            $sql .= " ORDER BY ms.scheduled_date";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Get Upcoming Maintenance Schedules Error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get overdue maintenance schedules
     * 
     * @param int $hospitalId Optional hospital ID to filter schedules
     * @return array The overdue schedules
     */
    public function getOverdueSchedules($hospitalId = null) {
        try {
            $sql = "
                SELECT ms.*, d.name AS device_name, d.serial_number,
                       h.name AS hospital_name, dp.name AS department_name,
                       u.full_name AS created_by_name
                FROM maintenance_schedules ms
                JOIN devices d ON ms.device_id = d.id
                JOIN hospitals h ON d.hospital_id = h.id
                JOIN departments dp ON d.department_id = dp.id
                JOIN users u ON ms.created_by = u.id
                WHERE ms.status = 'scheduled'
                AND ms.scheduled_date < CURDATE()
            ";
            $params = [];
            
            if ($hospitalId) {
                $sql .= " AND d.hospital_id = ?";
                $params[] = $hospitalId;
            }
            
            $sql .= " ORDER BY ms.scheduled_date";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Get Overdue Maintenance Schedules Error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Calculate the next schedule date based on frequency
     * 
     * @param string $currentDate The current schedule date
     * @param string $frequency The frequency (daily, weekly, monthly, quarterly, yearly)
     * @return string The next schedule date
     */
    private function calculateNextScheduleDate($currentDate, $frequency) {
        $date = new DateTime($currentDate);
        
        switch ($frequency) {
            case 'daily':
                $date->add(new DateInterval('P1D'));
                break;
                
            case 'weekly':
                $date->add(new DateInterval('P1W'));
                break;
                
            case 'monthly':
                $date->add(new DateInterval('P1M'));
                break;
                
            case 'quarterly':
                $date->add(new DateInterval('P3M'));
                break;
                
            case 'yearly':
                $date->add(new DateInterval('P1Y'));
                break;
        }
        
        return $date->format('Y-m-d');
    }
    
    /**
     * Update overdue maintenance schedules
     *
     * @return int The number of updated schedules
     */
    public function updateOverdueSchedules() {
        try {
            $stmt = $this->pdo->prepare("
                UPDATE maintenance_schedules
                SET status = 'overdue'
                WHERE status = 'scheduled'
                AND scheduled_date < CURDATE()
            ");

            $stmt->execute();
            return $stmt->rowCount();
        } catch (PDOException $e) {
            error_log("Update Overdue Maintenance Schedules Error: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Count completed maintenance by user
     *
     * @param int $userId The user ID
     * @return int The number of completed maintenance
     */
    public function countCompletedByUser($userId) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT COUNT(*) FROM maintenance_logs
                WHERE performed_by = ? AND status = 'completed'
            ");
            $stmt->execute([$userId]);

            return (int) $stmt->fetchColumn();
        } catch (PDOException $e) {
            error_log("Count Completed Maintenance By User Error: " . $e->getMessage());
            return 0;
        }
    }
}
