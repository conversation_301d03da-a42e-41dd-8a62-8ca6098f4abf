<?php
/**
 * Hospitals Controller
 * 
 * This file handles hospital-related operations.
 */

// Check if the user is logged in
requireLogin();

// Get the current user
$currentUser = getCurrentUser();
$userHospitalId = $currentUser['hospital_id'];

// Handle actions
$action = isset($url[1]) ? $url[1] : 'index';
$hospitalId = isset($url[2]) ? (int)$url[2] : 0;

switch ($action) {
    case 'index':
        // Check if the user has permission to view hospitals
        if (!hasPermission('view_hospitals') && !hasPermission('manage_hospitals')) {
            setFlashMessage('error', 'Access denied');
            redirect('dashboard');
        }
        
        // Get all hospitals
        if ($userHospitalId && !hasPermission('manage_hospitals')) {
            // Non-admin users with hospital assignment can only see their hospital
            $hospitals = [$hospitalModel->getById($userHospitalId)];
        } else {
            // Admin can see all hospitals
            $hospitals = $hospitalModel->getAll();
        }

        // Ensure hospitals is an array
        if (!is_array($hospitals)) {
            $hospitals = [];
        }

        // Add additional data for each hospital
        foreach ($hospitals as &$hospital) {
            // Add department count
            $hospital['department_count'] = $departmentModel->countByHospital($hospital['id']);

            // Add device count
            $hospital['device_count'] = $deviceModel->countByHospital($hospital['id']);

            // Add default values if missing
            if (!isset($hospital['city'])) $hospital['city'] = '';
            if (!isset($hospital['country'])) $hospital['country'] = '';
            if (!isset($hospital['email'])) $hospital['email'] = '';
        }
        
        // Include the hospitals index view
        include 'views/hospitals/index.php';
        break;
        
    case 'create':
        // Check if the user has permission to manage hospitals
        requirePermission('manage_hospitals');
        
        // Handle form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $hospitalData = [
                'name' => sanitize($_POST['name'] ?? ''),
                'address' => sanitize($_POST['address'] ?? ''),
                'phone' => sanitize($_POST['phone'] ?? ''),
                'email' => sanitize($_POST['email'] ?? '')
            ];
            
            // Validate data
            $errors = [];
            
            if (empty($hospitalData['name'])) {
                $errors['name'] = __('required_field');
            }

            if (empty($hospitalData['address'])) {
                $errors['address'] = __('required_field');
            }

            if (!empty($hospitalData['email']) && !filter_var($hospitalData['email'], FILTER_VALIDATE_EMAIL)) {
                $errors['email'] = __('invalid_email');
            }
            
            // If no errors, create the hospital
            if (empty($errors)) {
                $hospitalId = $hospitalModel->create($hospitalData);
                
                if ($hospitalId) {
                    // Log the action
                    logAction('create_hospital', 'Created hospital: ' . $hospitalData['name']);
                    
                    // Set flash message
                    setFlashMessage('success', 'Hospital created successfully');

                    // Redirect to hospitals index
                    redirect('hospitals');
                } else {
                    $errors['general'] = 'Failed to create hospital';
                }
            }
        } else {
            // Initialize empty hospital data
            $hospitalData = [
                'name' => '',
                'address' => '',
                'phone' => '',
                'email' => ''
            ];
            
            $errors = [];
        }

        // Define countries list
        $countries = [
            'Afghanistan', 'Albania', 'Algeria', 'Argentina', 'Australia', 'Austria', 'Bangladesh', 'Belgium', 'Brazil', 'Canada',
            'China', 'Denmark', 'Egypt', 'Finland', 'France', 'Germany', 'Greece', 'India', 'Indonesia', 'Iran',
            'Iraq', 'Ireland', 'Italy', 'Japan', 'Jordan', 'Kuwait', 'Lebanon', 'Malaysia', 'Mexico', 'Netherlands',
            'New Zealand', 'Norway', 'Pakistan', 'Philippines', 'Poland', 'Portugal', 'Qatar', 'Russia', 'Saudi Arabia', 'Singapore',
            'South Africa', 'South Korea', 'Spain', 'Sweden', 'Switzerland', 'Thailand', 'Turkey', 'UAE', 'United Kingdom', 'United States'
        ];

        // Include the hospitals create view
        include 'views/hospitals/create.php';
        break;

    case 'store':
        // Check if the user has permission to manage hospitals
        requirePermission('manage_hospitals');

        // Handle form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $hospitalData = [
                'name' => $_POST['name'] ?? '',
                'address' => $_POST['address'] ?? '',
                'city' => $_POST['city'] ?? '',
                'country' => $_POST['country'] ?? '',
                'phone' => $_POST['phone'] ?? '',
                'email' => $_POST['email'] ?? '',
                'website' => $_POST['website'] ?? '',
                'notes' => $_POST['notes'] ?? ''
            ];

            // Validate data
            $errors = [];

            if (empty($hospitalData['name'])) {
                $errors['name'] = __('required_field');
            }

            if (empty($hospitalData['address'])) {
                $errors['address'] = __('required_field');
            }

            if (empty($hospitalData['city'])) {
                $errors['city'] = __('required_field');
            }

            if (empty($hospitalData['country'])) {
                $errors['country'] = __('required_field');
            }

            if (empty($hospitalData['phone'])) {
                $errors['phone'] = __('required_field');
            }

            if (empty($hospitalData['email'])) {
                $errors['email'] = __('required_field');
            } elseif (!filter_var($hospitalData['email'], FILTER_VALIDATE_EMAIL)) {
                $errors['email'] = __('invalid_email');
            }

            if (!empty($hospitalData['website']) && !filter_var($hospitalData['website'], FILTER_VALIDATE_URL)) {
                $errors['website'] = __('invalid_url');
            }

            // If no errors, create the hospital
            if (empty($errors)) {
                $hospitalId = $hospitalModel->create($hospitalData);

                if ($hospitalId) {
                    // Log the action
                    logAction('create_hospital', 'Created hospital: ' . $hospitalData['name']);

                    // Clear any stored form data
                    unset($_SESSION['form_data']);

                    // Set success message and redirect
                    setFlashMessage('success', __('hospital') . ' "' . $hospitalData['name'] . '" ' . __('created_successfully'));
                    redirect('hospitals');
                } else {
                    $errors['general'] = __('create_failed');
                }
            }

            // If there are errors, redirect back to create form with errors
            if (!empty($errors)) {
                $_SESSION['form_errors'] = $errors;
                $_SESSION['form_data'] = $hospitalData;
                redirect('hospitals/create');
            }
        } else {
            // If not POST request, redirect to create form
            redirect('hospitals/create');
        }
        break;

    case 'edit':
        // Check if the user has permission to manage hospitals
        requirePermission('manage_hospitals');
        
        // Get the hospital
        $hospital = $hospitalModel->getById($hospitalId);
        
        // Check if the hospital exists
        if (!$hospital) {
            setFlashMessage('error', 'Hospital not found');
            redirect('hospitals');
        }
        
        // Handle form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $hospitalData = [
                'name' => $_POST['name'] ?? $hospital['name'],
                'address' => $_POST['address'] ?? $hospital['address'],
                'phone' => $_POST['phone'] ?? $hospital['phone'],
                'email' => $_POST['email'] ?? $hospital['email']
            ];
            
            // Validate data
            $errors = [];
            
            if (empty($hospitalData['name'])) {
                $errors['name'] = __('required_field');
            }
            
            if (empty($hospitalData['address'])) {
                $errors['address'] = __('required_field');
            }
            
            if (!empty($hospitalData['email']) && !filter_var($hospitalData['email'], FILTER_VALIDATE_EMAIL)) {
                $errors['email'] = __('invalid_email');
            }
            
            // If no errors, update the hospital
            if (empty($errors)) {
                $result = $hospitalModel->update($hospitalId, $hospitalData);
                
                if ($result) {
                    // Log the action
                    logAction('update_hospital', 'Updated hospital: ' . $hospitalData['name']);
                    
                    // Set flash message
                    setFlashMessage('success', 'Hospital updated successfully');

                    // Redirect to hospitals index
                    redirect('hospitals');
                } else {
                    $errors['general'] = 'Failed to update hospital';
                }
            }
        } else {
            $hospitalData = $hospital;
            $errors = [];
        }

        // Define countries list
        $countries = [
            'Afghanistan', 'Albania', 'Algeria', 'Argentina', 'Australia', 'Austria', 'Bangladesh', 'Belgium', 'Brazil', 'Canada',
            'China', 'Denmark', 'Egypt', 'Finland', 'France', 'Germany', 'Greece', 'India', 'Indonesia', 'Iran',
            'Iraq', 'Ireland', 'Italy', 'Japan', 'Jordan', 'Kuwait', 'Lebanon', 'Malaysia', 'Mexico', 'Netherlands',
            'New Zealand', 'Norway', 'Pakistan', 'Philippines', 'Poland', 'Portugal', 'Qatar', 'Russia', 'Saudi Arabia', 'Singapore',
            'South Africa', 'South Korea', 'Spain', 'Sweden', 'Switzerland', 'Thailand', 'Turkey', 'UAE', 'United Kingdom', 'United States'
        ];

        // Include the hospitals edit view
        include 'views/hospitals/edit.php';
        break;
        
    case 'delete':
        // Check if the user has permission to manage hospitals
        requirePermission('manage_hospitals');
        
        // Get the hospital
        $hospital = $hospitalModel->getById($hospitalId);
        
        // Check if the hospital exists
        if (!$hospital) {
            setFlashMessage('error', __('not_found'));
            redirect('hospitals');
        }
        
        // Handle form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['confirm']) && $_POST['confirm'] === 'yes') {
            $result = $hospitalModel->delete($hospitalId);
            
            if ($result) {
                // Log the action
                logAction('delete_hospital', 'Deleted hospital: ' . $hospital['name']);

                // Set flash message
                setFlashMessage('success', 'Hospital deleted successfully');
            } else {
                setFlashMessage('error', 'Failed to delete hospital');
            }
            
            // Redirect to hospitals index
            redirect('hospitals');
        }
        
        // Include the hospitals delete view
        include 'views/hospitals/delete.php';
        break;
        
    case 'view':
        // Check if the user has permission to view hospitals
        if (!hasPermission('view_hospitals') && !hasPermission('manage_hospitals')) {
            setFlashMessage('error', 'Access denied');
            redirect('dashboard');
        }

        // Get the hospital
        $hospital = $hospitalModel->getById($hospitalId);

        // Check if the hospital exists
        if (!$hospital) {
            setFlashMessage('error', 'Hospital not found');
            redirect('hospitals');
        }

        // Check if the user has permission to view this hospital
        if ($userHospitalId && $hospital['id'] != $userHospitalId && !hasPermission('manage_hospitals')) {
            setFlashMessage('error', 'Access denied');
            redirect('hospitals');
        }
        
        // Get hospital statistics
        $stats = $hospitalModel->getStatistics($hospitalId);
        
        // Get departments in this hospital
        $departments = $departmentModel->getByHospital($hospitalId);
        
        // Get devices in this hospital
        $devices = $deviceModel->getAll($hospitalId);
        
        // Include the hospitals view
        include 'views/hospitals/view.php';
        break;
        
    default:
        // 404 Not Found
        header("HTTP/1.0 404 Not Found");
        include 'views/404.php';
        break;
}
