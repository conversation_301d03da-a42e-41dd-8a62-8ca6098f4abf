<?php
/**
 * Login Test Script
 * This script tests database connection and user authentication
 */

// Include database configuration
require_once 'config/database.php';

echo "<h2>Login Test Results</h2>";

// Test database connection
try {
    echo "<p><strong>Database Connection:</strong> ";
    if (isset($pdo)) {
        echo "SUCCESS</p>";
        
        // Check if users table exists
        $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
        if ($stmt->rowCount() > 0) {
            echo "<p><strong>Users Table:</strong> EXISTS</p>";
            
            // Check if admin user exists
            $stmt = $pdo->prepare("SELECT * FROM users WHERE username = ?");
            $stmt->execute(['admin']);
            $adminUser = $stmt->fetch();
            
            if ($adminUser) {
                echo "<p><strong>Admin User:</strong> EXISTS</p>";
                echo "<p><strong>Admin Details:</strong></p>";
                echo "<ul>";
                echo "<li>ID: " . $adminUser['id'] . "</li>";
                echo "<li>Username: " . htmlspecialchars($adminUser['username']) . "</li>";
                echo "<li>Email: " . htmlspecialchars($adminUser['email']) . "</li>";
                echo "<li>Full Name: " . htmlspecialchars($adminUser['full_name']) . "</li>";
                echo "<li>Role: " . htmlspecialchars($adminUser['role']) . "</li>";
                echo "<li>Status: " . htmlspecialchars($adminUser['status']) . "</li>";
                echo "<li>Hospital ID: " . ($adminUser['hospital_id'] ?? 'NULL') . "</li>";
                echo "<li>Password Hash: " . substr($adminUser['password'], 0, 20) . "...</li>";
                echo "</ul>";
                
                // Test password verification
                $testPasswords = ['admin123', 'Admin123', 'admin', 'password'];
                echo "<p><strong>Password Tests:</strong></p>";
                echo "<ul>";
                foreach ($testPasswords as $testPass) {
                    $result = password_verify($testPass, $adminUser['password']);
                    echo "<li>Password '$testPass': " . ($result ? '<span style="color:green">VALID</span>' : '<span style="color:red">INVALID</span>') . "</li>";
                }
                echo "</ul>";
                
            } else {
                echo "<p><strong>Admin User:</strong> <span style='color:red'>NOT FOUND</span></p>";
                
                // Check all users
                $stmt = $pdo->query("SELECT username, email, role, status FROM users");
                $users = $stmt->fetchAll();
                
                if ($users) {
                    echo "<p><strong>Existing Users:</strong></p>";
                    echo "<ul>";
                    foreach ($users as $user) {
                        echo "<li>" . htmlspecialchars($user['username']) . " (" . htmlspecialchars($user['role']) . ") - " . htmlspecialchars($user['status']) . "</li>";
                    }
                    echo "</ul>";
                } else {
                    echo "<p><strong>No users found in database</strong></p>";
                }
            }
            
        } else {
            echo "<p><strong>Users Table:</strong> <span style='color:red'>NOT FOUND</span></p>";
        }
        
    } else {
        echo "<span style='color:red'>FAILED - PDO not initialized</span></p>";
    }
    
} catch (Exception $e) {
    echo "<span style='color:red'>ERROR: " . htmlspecialchars($e->getMessage()) . "</span></p>";
}

echo "<hr>";
echo "<p><strong>Next Steps:</strong></p>";
echo "<ul>";
echo "<li>If admin user doesn't exist, run the installation: <a href='database/install.php'>database/install.php</a></li>";
echo "<li>If password tests fail, the default password might be different</li>";
echo "<li>Check the installation guide for proper setup steps</li>";
echo "</ul>";
?>
