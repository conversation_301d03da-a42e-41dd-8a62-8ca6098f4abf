<?php
/**
 * Maintenance Controller
 * 
 * This file handles maintenance-related operations.
 */

// Check if the user is logged in
requireLogin();

// Get the current user
$currentUser = getCurrentUser();
$userHospitalId = $currentUser['hospital_id'];

// Handle actions
$action = isset($url[1]) ? $url[1] : 'index';
$id = isset($url[2]) ? (int)$url[2] : 0;

switch ($action) {
    case 'index':
        // Check if the user has permission to view maintenance
        if (!hasPermission('view_maintenance') && !hasPermission('manage_maintenance')) {
            setFlashMessage('error', __('access_denied'));
            redirect('dashboard');
        }
        
        // Get filter parameters
        $hospitalId = isset($_GET['hospital_id']) ? (int)$_GET['hospital_id'] : $userHospitalId;
        $deviceId = isset($_GET['device_id']) ? (int)$_GET['device_id'] : null;
        $status = isset($_GET['status']) ? $_GET['status'] : null;
        
        // Get maintenance schedules
        $schedules = $maintenanceModel->getAllSchedules($deviceId, $hospitalId);
        
        // Filter by status if specified
        if ($status) {
            $schedules = array_filter($schedules, function($schedule) use ($status) {
                return $schedule['status'] === $status;
            });
        }
        
        // Get hospitals and devices for the filters
        if (hasPermission('manage_hospitals')) {
            $hospitals = $hospitalModel->getAll();
        } elseif ($userHospitalId) {
            $hospitals = [$hospitalModel->getById($userHospitalId)];
        } else {
            $hospitals = [];
        }
        
        if ($hospitalId) {
            $devices = $deviceModel->getAll($hospitalId);
        } else {
            $devices = [];
        }
        
        // Include the maintenance index view
        include 'views/maintenance/index.php';
        break;
        
    case 'logs':
        // Check if the user has permission to view maintenance
        if (!hasPermission('view_maintenance') && !hasPermission('manage_maintenance')) {
            setFlashMessage('error', __('access_denied'));
            redirect('dashboard');
        }
        
        // Get filter parameters
        $hospitalId = isset($_GET['hospital_id']) ? (int)$_GET['hospital_id'] : $userHospitalId;
        $deviceId = isset($_GET['device_id']) ? (int)$_GET['device_id'] : null;
        $scheduleId = isset($_GET['schedule_id']) ? (int)$_GET['schedule_id'] : null;
        
        // Get maintenance logs
        $logs = $maintenanceModel->getAllLogs($scheduleId, $deviceId, $hospitalId);
        
        // Get hospitals and devices for the filters
        if (hasPermission('manage_hospitals')) {
            $hospitals = $hospitalModel->getAll();
        } elseif ($userHospitalId) {
            $hospitals = [$hospitalModel->getById($userHospitalId)];
        } else {
            $hospitals = [];
        }
        
        if ($hospitalId) {
            $devices = $deviceModel->getAll($hospitalId);
        } else {
            $devices = [];
        }
        
        // Include the maintenance logs view
        include 'views/maintenance/logs.php';
        break;
        
    case 'create_schedule':
        // Check if the user has permission to manage maintenance
        requirePermission('manage_maintenance');
        
        // Get device ID from query string if provided
        $deviceId = isset($_GET['device_id']) ? (int)$_GET['device_id'] : null;
        
        // Handle form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $scheduleData = [
                'device_id' => $_POST['device_id'] ?? null,
                'title' => sanitize($_POST['title'] ?? ''),
                'description' => sanitize($_POST['description'] ?? ''),
                'scheduled_date' => $_POST['scheduled_date'] ?? date('Y-m-d'),
                'frequency' => $_POST['frequency'] ?? 'once',
                'status' => 'scheduled',
                'created_by' => $currentUser['id']
            ];
            
            // Validate data
            $errors = [];
            
            if (empty($scheduleData['device_id'])) {
                $errors['device_id'] = __('required_field');
            } else {
                // Check if the device exists
                $device = $deviceModel->getById($scheduleData['device_id']);
                if (!$device) {
                    $errors['device_id'] = __('invalid_device');
                } elseif ($userHospitalId && $device['hospital_id'] != $userHospitalId && !hasPermission('manage_hospitals')) {
                    $errors['device_id'] = __('access_denied');
                }
            }
            
            if (empty($scheduleData['title'])) {
                $errors['title'] = __('required_field');
            }
            
            if (empty($scheduleData['scheduled_date'])) {
                $errors['scheduled_date'] = __('required_field');
            } elseif (strtotime($scheduleData['scheduled_date']) < strtotime('today')) {
                $errors['scheduled_date'] = __('date_in_past');
            }
            
            // If no errors, create the schedule
            if (empty($errors)) {
                $scheduleId = $maintenanceModel->createSchedule($scheduleData);
                
                if ($scheduleId) {
                    // Log the action
                    logAction('create_maintenance_schedule', 'Created maintenance schedule: ' . $scheduleData['title']);
                    
                    // Set flash message
                    setFlashMessage('success', __('created_successfully', [__('maintenance_schedule')]));
                    
                    // Redirect to maintenance index
                    redirect('maintenance');
                } else {
                    $errors['general'] = __('create_failed');
                }
            }
        } else {
            // Initialize empty schedule data
            $scheduleData = [
                'device_id' => $deviceId,
                'title' => '',
                'description' => '',
                'scheduled_date' => date('Y-m-d'),
                'frequency' => 'once'
            ];
            
            $errors = [];
        }
        
        // Get hospitals for the dropdown
        if (hasPermission('manage_hospitals')) {
            $hospitals = $hospitalModel->getAll();
        } elseif ($userHospitalId) {
            $hospitals = [$hospitalModel->getById($userHospitalId)];
        } else {
            $hospitals = [];
        }
        
        // Get devices for the dropdown
        if ($userHospitalId) {
            $devices = $deviceModel->getAll($userHospitalId);
        } else {
            $devices = $deviceModel->getAll();
        }
        
        // Include the maintenance create schedule view
        include 'views/maintenance/create_schedule.php';
        break;
        
    case 'edit_schedule':
        // Check if the user has permission to manage maintenance
        requirePermission('manage_maintenance');
        
        // Get the schedule
        $schedule = $maintenanceModel->getScheduleById($id);
        
        // Check if the schedule exists
        if (!$schedule) {
            setFlashMessage('error', __('not_found'));
            redirect('maintenance');
        }
        
        // Get the device
        $device = $deviceModel->getById($schedule['device_id']);
        
        // Check if the user has permission to edit this schedule
        if ($userHospitalId && $device['hospital_id'] != $userHospitalId && !hasPermission('manage_hospitals')) {
            setFlashMessage('error', __('access_denied'));
            redirect('maintenance');
        }
        
        // Handle form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $scheduleData = [
                'device_id' => $_POST['device_id'] ?? $schedule['device_id'],
                'title' => $_POST['title'] ?? $schedule['title'],
                'description' => $_POST['description'] ?? $schedule['description'],
                'scheduled_date' => $_POST['scheduled_date'] ?? $schedule['scheduled_date'],
                'frequency' => $_POST['frequency'] ?? $schedule['frequency'],
                'status' => $_POST['status'] ?? $schedule['status']
            ];
            
            // Validate data
            $errors = [];
            
            if (empty($scheduleData['device_id'])) {
                $errors['device_id'] = __('required_field');
            } else {
                // Check if the device exists
                $device = $deviceModel->getById($scheduleData['device_id']);
                if (!$device) {
                    $errors['device_id'] = __('invalid_device');
                } elseif ($userHospitalId && $device['hospital_id'] != $userHospitalId && !hasPermission('manage_hospitals')) {
                    $errors['device_id'] = __('access_denied');
                }
            }
            
            if (empty($scheduleData['title'])) {
                $errors['title'] = __('required_field');
            }
            
            if (empty($scheduleData['scheduled_date'])) {
                $errors['scheduled_date'] = __('required_field');
            }
            
            // If no errors, update the schedule
            if (empty($errors)) {
                $result = $maintenanceModel->updateSchedule($id, $scheduleData);
                
                if ($result) {
                    // Log the action
                    logAction('update_maintenance_schedule', 'Updated maintenance schedule: ' . $scheduleData['title']);
                    
                    // Set flash message
                    setFlashMessage('success', __('updated_successfully', [__('maintenance_schedule')]));
                    
                    // Redirect to maintenance index
                    redirect('maintenance');
                } else {
                    $errors['general'] = __('update_failed');
                }
            }
        } else {
            $scheduleData = $schedule;
            $errors = [];
        }
        
        // Get hospitals for the dropdown
        if (hasPermission('manage_hospitals')) {
            $hospitals = $hospitalModel->getAll();
        } elseif ($userHospitalId) {
            $hospitals = [$hospitalModel->getById($userHospitalId)];
        } else {
            $hospitals = [];
        }
        
        // Get devices for the dropdown
        if ($userHospitalId) {
            $devices = $deviceModel->getAll($userHospitalId);
        } else {
            $devices = $deviceModel->getAll();
        }
        
        // Include the maintenance edit schedule view
        include 'views/maintenance/edit_schedule.php';
        break;
        
    case 'delete_schedule':
        // Check if the user has permission to manage maintenance
        requirePermission('manage_maintenance');
        
        // Get the schedule
        $schedule = $maintenanceModel->getScheduleById($id);
        
        // Check if the schedule exists
        if (!$schedule) {
            setFlashMessage('error', __('not_found'));
            redirect('maintenance');
        }
        
        // Get the device
        $device = $deviceModel->getById($schedule['device_id']);
        
        // Check if the user has permission to delete this schedule
        if ($userHospitalId && $device['hospital_id'] != $userHospitalId && !hasPermission('manage_hospitals')) {
            setFlashMessage('error', __('access_denied'));
            redirect('maintenance');
        }
        
        // Handle form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['confirm']) && $_POST['confirm'] === 'yes') {
            $result = $maintenanceModel->deleteSchedule($id);
            
            if ($result) {
                // Log the action
                logAction('delete_maintenance_schedule', 'Deleted maintenance schedule: ' . $schedule['title']);
                
                // Set flash message
                setFlashMessage('success', __('deleted_successfully', [__('maintenance_schedule')]));
            } else {
                setFlashMessage('error', __('delete_failed'));
            }
            
            // Redirect to maintenance index
            redirect('maintenance');
        }
        
        // Include the maintenance delete schedule view
        include 'views/maintenance/delete_schedule.php';
        break;
        
    case 'create_log':
        // Check if the user has permission to manage maintenance
        requirePermission('manage_maintenance');
        
        // Get schedule ID from query string if provided
        $scheduleId = isset($_GET['schedule_id']) ? (int)$_GET['schedule_id'] : null;
        $deviceId = isset($_GET['device_id']) ? (int)$_GET['device_id'] : null;
        
        // If schedule ID is provided, get the schedule
        if ($scheduleId) {
            $schedule = $maintenanceModel->getScheduleById($scheduleId);
            if ($schedule) {
                $deviceId = $schedule['device_id'];
            }
        }
        
        // Handle form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $logData = [
                'maintenance_schedule_id' => $_POST['maintenance_schedule_id'] ?? null,
                'device_id' => $_POST['device_id'] ?? null,
                'performed_by' => $currentUser['id'],
                'performed_date' => $_POST['performed_date'] ?? date('Y-m-d'),
                'notes' => $_POST['notes'] ?? '',
                'status' => $_POST['status'] ?? 'completed'
            ];
            
            // Validate data
            $errors = [];
            
            if (empty($logData['device_id'])) {
                $errors['device_id'] = __('required_field');
            } else {
                // Check if the device exists
                $device = $deviceModel->getById($logData['device_id']);
                if (!$device) {
                    $errors['device_id'] = __('invalid_device');
                } elseif ($userHospitalId && $device['hospital_id'] != $userHospitalId && !hasPermission('manage_hospitals')) {
                    $errors['device_id'] = __('access_denied');
                }
            }
            
            if (empty($logData['performed_date'])) {
                $errors['performed_date'] = __('required_field');
            }
            
            // If no errors, create the log
            if (empty($errors)) {
                $logId = $maintenanceModel->createLog($logData);
                
                if ($logId) {
                    // Log the action
                    logAction('create_maintenance_log', 'Created maintenance log for device ID: ' . $logData['device_id']);
                    
                    // Set flash message
                    setFlashMessage('success', __('created_successfully', [__('maintenance_log')]));
                    
                    // Redirect to maintenance logs
                    redirect('maintenance/logs');
                } else {
                    $errors['general'] = __('create_failed');
                }
            }
        } else {
            // Initialize empty log data
            $logData = [
                'maintenance_schedule_id' => $scheduleId,
                'device_id' => $deviceId,
                'performed_date' => date('Y-m-d'),
                'notes' => '',
                'status' => 'completed'
            ];
            
            $errors = [];
        }
        
        // Get hospitals for the dropdown
        if (hasPermission('manage_hospitals')) {
            $hospitals = $hospitalModel->getAll();
        } elseif ($userHospitalId) {
            $hospitals = [$hospitalModel->getById($userHospitalId)];
        } else {
            $hospitals = [];
        }
        
        // Get devices for the dropdown
        if ($userHospitalId) {
            $devices = $deviceModel->getAll($userHospitalId);
        } else {
            $devices = $deviceModel->getAll();
        }
        
        // Get schedules for the dropdown
        if ($deviceId) {
            $schedules = $maintenanceModel->getAllSchedules($deviceId);
        } else {
            $schedules = [];
        }
        
        // Include the maintenance create log view
        include 'views/maintenance/create_log.php';
        break;
        
    case 'view_schedule':
        // Check if the user has permission to view maintenance
        if (!hasPermission('view_maintenance') && !hasPermission('manage_maintenance')) {
            setFlashMessage('error', __('access_denied'));
            redirect('dashboard');
        }
        
        // Get the schedule
        $schedule = $maintenanceModel->getScheduleById($id);
        
        // Check if the schedule exists
        if (!$schedule) {
            setFlashMessage('error', __('not_found'));
            redirect('maintenance');
        }
        
        // Get the device
        $device = $deviceModel->getById($schedule['device_id']);
        
        // Check if the user has permission to view this schedule
        if ($userHospitalId && $device['hospital_id'] != $userHospitalId && !hasPermission('manage_hospitals')) {
            setFlashMessage('error', __('access_denied'));
            redirect('maintenance');
        }
        
        // Get maintenance logs for this schedule
        $logs = $maintenanceModel->getAllLogs($id);
        
        // Include the maintenance view schedule view
        include 'views/maintenance/view_schedule.php';
        break;
        
    case 'view_log':
        // Check if the user has permission to view maintenance
        if (!hasPermission('view_maintenance') && !hasPermission('manage_maintenance')) {
            setFlashMessage('error', __('access_denied'));
            redirect('dashboard');
        }
        
        // Get the log
        $log = $maintenanceModel->getLogById($id);
        
        // Check if the log exists
        if (!$log) {
            setFlashMessage('error', __('not_found'));
            redirect('maintenance/logs');
        }
        
        // Get the device
        $device = $deviceModel->getById($log['device_id']);
        
        // Check if the user has permission to view this log
        if ($userHospitalId && $device['hospital_id'] != $userHospitalId && !hasPermission('manage_hospitals')) {
            setFlashMessage('error', __('access_denied'));
            redirect('maintenance/logs');
        }
        
        // Include the maintenance view log view
        include 'views/maintenance/view_log.php';
        break;
        
    default:
        // 404 Not Found
        header("HTTP/1.0 404 Not Found");
        include 'views/404.php';
        break;
}
