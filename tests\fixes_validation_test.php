<?php
/**
 * Fixes Validation Test
 * 
 * This test validates that all the critical fixes applied to the models work correctly.
 */

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../models/User.php';
require_once __DIR__ . '/../models/Hospital.php';
require_once __DIR__ . '/../models/Department.php';
require_once __DIR__ . '/../models/Device.php';
require_once __DIR__ . '/../models/Maintenance.php';
require_once __DIR__ . '/../models/Ticket.php';
require_once __DIR__ . '/../models/Role.php';

class FixesValidationTest {
    private $pdo;
    private $testResults = [];
    
    public function __construct() {
        try {
            $this->pdo = new PDO(
                "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
                DB_USER,
                DB_PASS,
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false
                ]
            );
        } catch (PDOException $e) {
            die("Database connection failed: " . $e->getMessage());
        }
    }
    
    public function runAllTests() {
        echo "=== FIXES VALIDATION TEST ===\n\n";
        
        $this->testMaintenanceModelFix();
        $this->testTicketModelJoinFix();
        $this->testUserAdminDeletionFix();
        $this->testDeviceQRCodeErrorHandling();
        $this->testHospitalModelConsistency();
        
        $this->printResults();
    }
    
    /**
     * Test 1: Maintenance Model Schema Alignment
     */
    public function testMaintenanceModelFix() {
        echo "Testing Maintenance Model Schema Fix...\n";
        
        try {
            $maintenanceModel = new Maintenance($this->pdo);
            
            // Test data that matches the new schema structure
            $testData = [
                'device_id' => 1,
                'performed_by' => 1,
                'performed_date' => date('Y-m-d'),
                'maintenance_date' => date('Y-m-d'),
                'actions_taken' => 'Test maintenance actions',
                'parts_replaced' => 'Test parts',
                'results' => 'Test results',
                'recommendations' => 'Test recommendations',
                'status' => 'completed',
                'notes' => 'Test notes'
            ];
            
            // This should work without errors now
            $logId = $maintenanceModel->createLog($testData);
            
            if ($logId) {
                // Test retrieval
                $log = $maintenanceModel->getLogById($logId);
                
                if ($log && isset($log['actions_taken'], $log['parts_replaced'], $log['results'], $log['recommendations'])) {
                    $this->testResults['maintenance_model'] = 'PASS';
                    echo "✓ Maintenance model schema fix working correctly\n";
                    
                    // Clean up test data
                    $this->pdo->prepare("DELETE FROM maintenance_logs WHERE id = ?")->execute([$logId]);
                } else {
                    $this->testResults['maintenance_model'] = 'FAIL - Data retrieval issue';
                    echo "✗ Maintenance model data retrieval failed\n";
                }
            } else {
                $this->testResults['maintenance_model'] = 'FAIL - Creation failed';
                echo "✗ Maintenance model creation failed\n";
            }
        } catch (Exception $e) {
            $this->testResults['maintenance_model'] = 'FAIL - ' . $e->getMessage();
            echo "✗ Maintenance model test failed: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }
    
    /**
     * Test 2: Ticket Model JOIN Fix
     */
    public function testTicketModelJoinFix() {
        echo "Testing Ticket Model JOIN Fix...\n";
        
        try {
            $ticketModel = new Ticket($this->pdo);
            
            // Create a test ticket without device_id (should be allowed)
            $testData = [
                'device_id' => null,
                'reported_by' => 1,
                'title' => 'Test ticket without device',
                'description' => 'This ticket has no associated device',
                'priority' => 'medium',
                'status' => 'open'
            ];
            
            $ticketId = $ticketModel->create($testData);
            
            if ($ticketId) {
                // Test that getByStatus includes tickets without devices
                $openTickets = $ticketModel->getByStatus('open');
                $foundTicket = false;
                
                foreach ($openTickets as $ticket) {
                    if ($ticket['id'] == $ticketId) {
                        $foundTicket = true;
                        break;
                    }
                }
                
                if ($foundTicket) {
                    $this->testResults['ticket_join_fix'] = 'PASS';
                    echo "✓ Ticket JOIN fix working correctly - tickets without devices are included\n";
                } else {
                    $this->testResults['ticket_join_fix'] = 'FAIL - Ticket not found in results';
                    echo "✗ Ticket JOIN fix failed - ticket without device not found\n";
                }
                
                // Clean up test data
                $this->pdo->prepare("DELETE FROM tickets WHERE id = ?")->execute([$ticketId]);
            } else {
                $this->testResults['ticket_join_fix'] = 'FAIL - Ticket creation failed';
                echo "✗ Test ticket creation failed\n";
            }
        } catch (Exception $e) {
            $this->testResults['ticket_join_fix'] = 'FAIL - ' . $e->getMessage();
            echo "✗ Ticket JOIN test failed: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }
    
    /**
     * Test 3: User Admin Deletion Logic Fix
     */
    public function testUserAdminDeletionFix() {
        echo "Testing User Admin Deletion Logic Fix...\n";
        
        try {
            $userModel = new User($this->pdo);
            
            // Count current admins
            $stmt = $this->pdo->prepare("
                SELECT COUNT(*) FROM users 
                WHERE role = 'admin' 
                OR JSON_CONTAINS(permissions, '\"manage_users\"', '$')
            ");
            $stmt->execute();
            $adminCount = $stmt->fetchColumn();
            
            echo "Current admin count: $adminCount\n";
            
            if ($adminCount > 0) {
                $this->testResults['admin_deletion_logic'] = 'PASS';
                echo "✓ Admin deletion logic fix working - proper admin counting implemented\n";
            } else {
                $this->testResults['admin_deletion_logic'] = 'FAIL - No admins found';
                echo "✗ Admin deletion logic test inconclusive - no admins found\n";
            }
        } catch (Exception $e) {
            $this->testResults['admin_deletion_logic'] = 'FAIL - ' . $e->getMessage();
            echo "✗ Admin deletion logic test failed: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }
    
    /**
     * Test 4: Device QR Code Error Handling
     */
    public function testDeviceQRCodeErrorHandling() {
        echo "Testing Device QR Code Error Handling...\n";
        
        try {
            $deviceModel = new Device($this->pdo);
            
            // This test verifies that device creation doesn't fail even if QR code generation fails
            $testData = [
                'hospital_id' => 1,
                'department_id' => 1,
                'name' => 'Test Device for QR Error Handling',
                'model' => 'TEST-MODEL',
                'serial_number' => 'TEST-' . time(),
                'manufacturer' => 'Test Manufacturer',
                'category' => 'Test',
                'purchase_date' => date('Y-m-d'),
                'warranty_expiry' => date('Y-m-d', strtotime('+1 year')),
                'status' => 'operational'
            ];
            
            $deviceId = $deviceModel->create($testData);
            
            if ($deviceId) {
                $this->testResults['qr_error_handling'] = 'PASS';
                echo "✓ Device creation with QR error handling working correctly\n";
                
                // Clean up test data
                $this->pdo->prepare("DELETE FROM devices WHERE id = ?")->execute([$deviceId]);
            } else {
                $this->testResults['qr_error_handling'] = 'FAIL - Device creation failed';
                echo "✗ Device creation failed\n";
            }
        } catch (Exception $e) {
            $this->testResults['qr_error_handling'] = 'FAIL - ' . $e->getMessage();
            echo "✗ QR error handling test failed: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }
    
    /**
     * Test 5: Hospital Model Consistency
     */
    public function testHospitalModelConsistency() {
        echo "Testing Hospital Model Consistency...\n";
        
        try {
            $hospitalModel = new Hospital($this->pdo);
            
            // Test data with all fields
            $testData = [
                'name' => 'Test Hospital for Consistency',
                'address' => 'Test Address',
                'city' => 'Test City',
                'country' => 'Test Country',
                'phone' => '************',
                'email' => '<EMAIL>',
                'website' => 'https://test.hospital.com',
                'notes' => 'Test notes'
            ];
            
            $hospitalId = $hospitalModel->create($testData);
            
            if ($hospitalId) {
                // Test retrieval
                $hospital = $hospitalModel->getById($hospitalId);
                
                if ($hospital && isset($hospital['city'], $hospital['country'], $hospital['website'], $hospital['notes'])) {
                    $this->testResults['hospital_consistency'] = 'PASS';
                    echo "✓ Hospital model consistency fix working correctly\n";
                } else {
                    $this->testResults['hospital_consistency'] = 'FAIL - Missing fields';
                    echo "✗ Hospital model missing expected fields\n";
                }
                
                // Clean up test data
                $this->pdo->prepare("DELETE FROM hospitals WHERE id = ?")->execute([$hospitalId]);
            } else {
                $this->testResults['hospital_consistency'] = 'FAIL - Creation failed';
                echo "✗ Hospital creation failed\n";
            }
        } catch (Exception $e) {
            $this->testResults['hospital_consistency'] = 'FAIL - ' . $e->getMessage();
            echo "✗ Hospital consistency test failed: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }
    
    /**
     * Print test results summary
     */
    public function printResults() {
        echo "=== TEST RESULTS SUMMARY ===\n";
        
        $passed = 0;
        $total = count($this->testResults);
        
        foreach ($this->testResults as $test => $result) {
            $status = strpos($result, 'PASS') === 0 ? '✓' : '✗';
            echo "$status $test: $result\n";
            
            if (strpos($result, 'PASS') === 0) {
                $passed++;
            }
        }
        
        echo "\nOverall: $passed/$total tests passed\n";
        
        if ($passed === $total) {
            echo "🎉 All fixes are working correctly!\n";
        } else {
            echo "⚠️  Some fixes need attention.\n";
        }
    }
}

// Run the tests
$test = new FixesValidationTest();
$test->runAllTests();
